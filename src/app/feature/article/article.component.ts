import { AsyncPipe, SlicePipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  DestroyRef,
  effect,
  ElementRef,
  inject,
  OnDestroy,
  OnInit,
  signal,
  viewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FormatDatePipe, SchemaOrgService, SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AnalyticsService,
  ApiResponseMeta,
  Article,
  ArticleAdvertisements,
  ArticleBody,
  ArticleBodyDetails,
  ArticleBodyType,
  ArticleCard,
  ArticleFileLinkDirective,
  ArticleReview,
  ArticleRouteParams,
  ArticleVideoComponent,
  AutoArticleBodyAdService,
  BreadcrumbItem,
  buildArticleUrl,
  ChartComponent,
  Column,
  DossierData,
  DossierRelatedArticle,
  FocusPointDirective,
  GalleryData,
  GalleryElementData,
  getStructuredDataForArticle,
  getStructuredDataForVideos,
  IconComponent,
  MinuteToMinuteBlock,
  MinuteToMinuteState,
  PAGE_TYPES,
  previewBackendArticleToArticleCard,
  ScrollPositionService,
  SearchBotService,
  SponsoredTag,
  VoteData,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { FormatPipeModule } from 'ngx-date-fns';
import { combineLatest, forkJoin } from 'rxjs';
import { map, switchMap, tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import {
  ADULT_CHOICE_STORAGE_KEY,
  AdultLayerComponent,
  AnchorToCommentsComponent,
  AppQuizComponent,
  ArticleCardComponent,
  ArticleCardType,
  AuthService,
  BorsSliderGalleryComponent,
  BreadcrumbComponent,
  defaultMetaInfo,
  DossierCardComponent,
  DossierCardType,
  ExternalRecommendationsComponent,
  GalleryService,
  GoogleNewsComponent,
  HeaderService,
  InformationBoxComponent,
  LinkifyPipe,
  LoginRedirectToken,
  makeBreadcrumbSchema,
  MinuteByMinuteTimelineComponent,
  PersonalizedRecommendationService,
  PrivateOpinionComponent,
  SecureApiService,
  SliderGalleryFullscreenLayerClickedEvent,
  SocialShareComponent,
  SponsoredTagBoxComponent,
  TenArticleRecommenderComponent,
  TenArticleRecommenderData,
  VotingComponent,
  VotingType,
  WysiwygBoxComponent,
} from '../../shared';
import { BackendArticleSocial } from '../comments/api/comment.definitions';
import { CommentService } from '../comments/api/comment.service';
import { CommentSectionComponent } from '../comments/components/comment-section/comment-section.component';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-article',
  templateUrl: './article.component.html',
  styleUrl: './article.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    SidebarComponent,
    WysiwygBoxComponent,
    AdultLayerComponent,
    AdvertisementAdoceanComponent,
    VotingComponent,
    CommentSectionComponent,
    MinuteByMinuteTimelineComponent,
    GoogleNewsComponent,
    BreadcrumbComponent,
    ArticleVideoComponent,
    FocusPointDirective,
    RouterLink,
    FormatPipeModule,
    SocialShareComponent,
    SlicePipe,
    IconComponent,
    ArticleCardComponent,
    DossierCardComponent,
    AppQuizComponent,
    FormatDatePipe,
    ArticleFileLinkDirective,
    BorsSliderGalleryComponent,
    LinkifyPipe,
    SponsoredTagBoxComponent,
    AsyncPipe,
    InformationBoxComponent,
    AnchorToCommentsComponent,
    PrivateOpinionComponent,
    TenArticleRecommenderComponent,
    ChartComponent,
    ExternalRecommendationsComponent,
  ],
  providers: [AutoArticleBodyAdService, FormatDatePipe],
})
export class ArticleComponent implements OnInit, OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly destroyRef = inject(DestroyRef);
  private readonly storage = inject(StorageService);
  private readonly articleBodyAdService = inject(AutoArticleBodyAdService);
  private readonly seoService = inject(SeoService);
  private readonly analyticsService = inject(AnalyticsService);
  private readonly searchBotService = inject(SearchBotService);
  private readonly formatDate = inject(FormatDatePipe);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  private readonly personalizedRecommendationService = inject(PersonalizedRecommendationService);
  private readonly utilService = inject(UtilService);
  private readonly commentService = inject(CommentService);
  private readonly headerService = inject(HeaderService);
  private readonly router = inject(Router);
  private readonly galleryService = inject(GalleryService);
  private readonly voteService = inject(VoteService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly schemaService = inject(SchemaOrgService);
  private readonly scrollPositionService = inject(ScrollPositionService);
  private readonly authService = inject(AuthService);
  private readonly secureApiService = inject(SecureApiService);
  private readonly storageService = inject(StorageService);

  readonly voteCache = this.voteService.voteCache;

  readonly article = signal<Article>({} as Article);
  readonly articleMeta = signal<ApiResponseMeta>({} as ApiResponseMeta);
  readonly isUserAdultChoice = signal<boolean>(false);
  readonly minuteToMinuteBlocks = signal<MinuteToMinuteBlock[]>([]);
  readonly canonicalUrl = signal<string>('');
  readonly isSearchBot = signal<boolean>(false);
  readonly articleSocials = signal<BackendArticleSocial | null>(null);
  readonly sponsoredTag = signal<SponsoredTag | null>(null);
  isExceptionAdvertEnabled = false;
  readonly dataTrigger = viewChild<ElementRef<HTMLDivElement>>('dataTrigger');
  adverts?: ArticleAdvertisements;
  interrupter?: ArticleAdvertisements;

  readonly breadcrumbItems = computed<BreadcrumbItem[]>(() => {
    const breadcrumbItems: BreadcrumbItem[] = [];
    const parentColumn: Column | undefined = this.article()?.primaryColumn?.parent;
    if (parentColumn) {
      breadcrumbItems.push({
        label: parentColumn.title,
        url: ['/', 'rovat', parentColumn?.slug],
      });
    }
    breadcrumbItems.push({
      label: this.article().columnTitle as string,
      url: ['/', 'rovat', this.article()?.columnSlug || ''],
    });
    return breadcrumbItems;
  });

  readonly ArticleBodyType = ArticleBodyType;
  readonly ArticleCardType = ArticleCardType;
  readonly DossierCardType = DossierCardType;
  readonly VotingType = VotingType;

  readonly galleries = signal<Record<string, GalleryData>>({});

  readonly adPageType = signal<string>(PAGE_TYPES.all_articles_and_sub_pages);

  readonly isBookmarked = signal<boolean>(false);
  readonly isLoggedIn = computed(() => {
    return this.utilService.isBrowser() && !!this.authService.currentUser();
  });

  constructor() {
    effect(() => {
      const dataTrigger = this.dataTrigger()?.nativeElement;
      if (!dataTrigger) {
        return;
      }
      if (!this.utilService.isBrowser()) {
        return;
      }
      if ('IntersectionObserver' in window) {
        this.observeArticleEnd();
      }
    });

    effect(() => {
      const loggedIn = this.isLoggedIn();
      const article = this.article();

      if (!loggedIn || !article) {
        this.isBookmarked.set(false);
        return;
      }

      this.secureApiService.getSavedArticle(article.id).subscribe({
        next: (res) => {
          this.isBookmarked.set(res.data.saved);
        },
        error: () => {
          this.isBookmarked.set(false);
        },
      });
    });
  }

  ngOnInit(): void {
    this.route.data
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        map(({ data }) => data)
      )
      .subscribe(({ article: articleResponse, url }) => {
        this.articleBodyAdService.init(articleResponse.data.body);
        const articleBody = this.articleBodyAdService.autoAd();
        const article = articleResponse.data;

        this.article.set({
          ...articleResponse.data,
          body: this.#prepareArticleBody(articleBody),
        });
        if (article) {
          // create an instance without reference
          const breadCrumbs: BreadcrumbItem[] = this.breadcrumbItems().slice(0, this.breadcrumbItems()?.length - 1);

          breadCrumbs.push({
            label: article?.title,
            url: buildArticleUrl(article),
          });
          const breadcrumbSchema = makeBreadcrumbSchema(breadCrumbs);
          this.schemaService.insertSchema(
            getStructuredDataForArticle(article, this.seoService.currentUrl, environment?.siteUrl ?? '', { hasAuthorPageSlug: true })
          );
          this.schemaService.insertSchema(breadcrumbSchema);
          for (const schema of getStructuredDataForVideos(this.article())) {
            this.schemaService.insertSchema(schema);
          }
        }

        const mainColor = this.article().primaryColumn?.mainColor;
        mainColor && this.headerService.setColor(mainColor);

        this.isUserAdultChoice.set((this.storage.getSessionStorageData(ADULT_CHOICE_STORAGE_KEY, false) ?? false) && article?.isAdultsOnly);
        this.articleMeta.set(articleResponse.meta);
        this.minuteToMinuteBlocks.set(article?.minuteToMinuteBlocks ?? []);
        this.adPageType.set(`column_${article?.primaryColumn?.parent?.slug || article?.primaryColumn?.slug}`);
        this.canonicalUrl.set(article?.seo?.seoCanonicalUrl || article?.canonicalUrl || (url ? `${this.seoService.hostUrl}/${url}` : this.seoService.hostUrl));
        this.sponsoredTag.set(articleResponse.meta?.['sponsoredTag']);

        this.loadEmbeddedGalleries();

        this.initComments();
        this.setMetaData();
        this.sendPageView();

        this.voteService.initArticleVotes(this.article());
      });

    this.initAds();
  }

  ngOnDestroy(): void {
    this.headerService.setColor(); //Resets the default colour.
    this.adStoreAdo.onArticleDestroy();
  }

  get isMinuteByMinute(): boolean {
    return this.article()?.minuteToMinute !== MinuteToMinuteState.NOT;
  }

  getMinuteByMinuteLink(id: string): string {
    return `${this.seoService.currentUrl.replace(/#.+$/, '')}#pp-${id}`;
  }

  onIsUserAdultChoose(isUserAdult: boolean): void {
    this.isUserAdultChoice.set(isUserAdult);
    this.adStoreAdo.setIsAdultPage(isUserAdult);
  }

  backendBodyArticleToArticleCard(articleBodyDetail: ArticleBodyDetails): ArticleCard {
    const article = articleBodyDetail.value;
    return {
      ...article,
      ...article?.dbcache,
    } as ArticleCard;
  }

  backendTenArticleRecommenderToData(element: ArticleBody): TenArticleRecommenderData {
    const { details } = element;
    const articleTypes = ['Detail.Reference.Article', 'Detail.Reference.ArticleOptional'];
    return {
      articles: details?.length
        ? details.filter(({ type, value }) => articleTypes.includes(type) && value).map((detail) => previewBackendArticleToArticleCard(detail.value))
        : [],
      withImage: details.find((item) => item.type.toString() === 'TenArticleRcm.TenArticleWithImageDetail')?.value,
    };
  }

  backendBodyDossierToDossierData(articleBodyDetail: ArticleBodyDetails): DossierData {
    const dossier = articleBodyDetail.value;
    return {
      slug: dossier.slug,
      title: dossier.title,
      secondaryArticles: (dossier?.relatedArticles ?? [])?.map(
        (
          article: DossierRelatedArticle & {
            isAdultType?: boolean;
          }
        ) => ({
          ...article,
          isAdultsOnly: article?.isAdultType,
        })
      ),
    } as DossierData;
  }

  getVoteData(value: VoteData): VoteDataWithAnswer {
    return this.voteService.getVoteData(value);
  }

  onVotingSubmit(votedId: string, voteData: VoteDataWithAnswer): void {
    this.voteService.onVotingSubmit(votedId, voteData).subscribe(() => this.cdr.detectChanges());
  }

  handleBookmarkClick(method: 'save' | 'delete'): void {
    if (!this.article().id) {
      return;
    }

    if (!this.isLoggedIn()) {
      this.storageService.setLocalStorageData(LoginRedirectToken, this.router.url);
      this.router.navigate(['/bejelentkezes']).then();
    }

    if (this.isLoggedIn() && method === 'save') {
      this.secureApiService.postSavedArticle(this.article().id).subscribe(() => {
        this.isBookmarked.set(true);
      });
    }

    if (this.isLoggedIn() && method === 'delete') {
      this.secureApiService.deleteSavedArticles(this.article().id).subscribe(() => {
        this.isBookmarked.set(false);
      });
    }
  }

  private sendPageView(): void {
    const lastUpdatedAt = (this.article()?.lastUpdated || this.article().publishDate) as Date;
    setTimeout(() => {
      this.analyticsService.sendPageView({
        pageCategory: this.article()?.primaryColumn?.parent?.slug || this.article()?.primaryColumn?.slug || '',
        customDim2: this.article()?.topicLevel1,
        customDim1: this.article()?.aniCode,
        title: this.article().title,
        articleSource: this.article()?.articleSource || 'no source',
        publishDate: this.formatDate.transform(this.article()?.publishDate as Date, 'dateTime'),
        lastUpdatedDate: this.formatDate.transform(lastUpdatedAt, 'dateTime'),
      });
    });
    this.isSearchBot.set(this.searchBotService.isSearchBot());
  }

  private initComments(): void {
    if (!this.utilService.isBrowser() || !this.article().id) {
      // No comments in SSR
      return;
    }

    this.commentService
      .getArticleSocials(this.article().id)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((articleSocials) => {
        this.articleSocials.set(articleSocials);
      });
  }

  private setMetaData(): void {
    const { lead, thumbnail, publicAuthor, publishDate, alternativeTitle } = this.article() || {};
    if (!this.article()) {
      return;
    }

    const finalTitle = alternativeTitle || this.article()?.seo?.seoTitle || this.article()?.title;
    const finalOgTitle = alternativeTitle || this.article()?.title;
    this.seoService.setMetaData(
      {
        title: finalTitle,
        description: this.article()?.seo?.seoDescription || lead || defaultMetaInfo.description,
        ogTitle: finalOgTitle,
        ogImage: thumbnail,
        ogType: 'article',
        articleAuthor: publicAuthor,
        articlePublishedTime: publishDate?.toISOString(),
        robots: this.article()?.seo?.seoRobotsMeta || defaultMetaInfo.robots + ', max-video-preview:5',
      },
      { skipSeoMetaCheck: true }
    );

    //By default, the updateCanonicalUrl prefixes the url with the host, so we pass a second parameter to force absolute url.
    this.seoService.updateCanonicalUrl(this.canonicalUrl(), { addHostUrl: false, skipSeoMetaCheck: true });
  }

  openGalleryDedicatedRouteLayer({ gallery, selectedImageIndex }: SliderGalleryFullscreenLayerClickedEvent): void {
    if (!gallery || !this.utilService.isBrowser()) {
      return;
    }

    const url = location.pathname;
    const galleryUrl = ['/', 'galeria', gallery.slug, ...(selectedImageIndex || selectedImageIndex === 0 ? [selectedImageIndex + 1] : [])];

    this.router.navigate(galleryUrl, { state: { referrerArticle: url } });
  }

  /**
   * When changing slides in the gallery we should send a pageView to Google Analytics and Gemius.
   * We need to explicitly send the href, title and referrers as these pageViews are just "virtual" views, because
   * they are not triggered by a real navigation in the browser.
   * @param gallery gallery that should receive the page views.
   * @param params parameters of the slide change event. For example the index of the image
   */
  handleGallerySlideChange(gallery: GalleryData, params: any): void {
    const { index } = params;
    const galleryUrl = [this.seoService.hostUrl, 'galeria', gallery.slug, ...(index ? [index + 1] : [])].join('/');
    const pageViewParams = { href: galleryUrl, title: gallery.title, referrer: this.seoService.currentUrl } as any;
    this.analyticsService.sendPageView(pageViewParams, 'Galéria');
    //pp_gemius_hit(environment.gemiusId, `page=${galleryUrl}`);
  }

  private loadEmbeddedGalleries(): void {
    const bodyElements = this.article()?.body ?? [];
    const gallerySubs = ((bodyElements ?? []) as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .filter((bodyElem) => !!bodyElem.details[0].value)
      .map((bodyElem: GalleryElementData) => this.galleryService.getGalleryDetails(bodyElem.details[0].value.slug));

    forkJoin(gallerySubs)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((galleries) => {
        galleries.forEach((gallery) => {
          const galleryData: GalleryData = {
            ...gallery,
            highlightedImageUrl: gallery.highlightedImage.url,
          } as GalleryData;
          this.galleries.update((galleries) => ({
            ...galleries,
            [gallery.id]: galleryData,
          }));
        });
        this.scrollPositionService.restoreScrollPosition();
      });
  }

  private observeArticleEnd(): void {
    const elem = this.dataTrigger()?.nativeElement;
    if (!elem) {
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting) {
          this.sendEcommerceEvent();
          if (elem) {
            observer.unobserve(elem);
          }
        }
      });
    });
    observer.observe(elem);
  }

  private sendEcommerceEvent(): void {
    const routeParams: ArticleRouteParams = this.route.snapshot.params as ArticleRouteParams;
    const article = this.article();
    if (!article) {
      return;
    }
    this.analyticsService.sendEcommerceEvent({
      id: `T${article.id}`,
      title: article.title,
      articleSlug: routeParams.articleSlug ? routeParams.articleSlug : 'cikk-elonezet',
      category: article.columnTitle ?? '',
      articleSource: article.articleSource ? article.articleSource : 'no source',
      publishDate: this.formatDate.transform(article.publishDate as Date, 'dateTime'),
      lastUpdatedDate: this.formatDate.transform((article.lastUpdated ? article.lastUpdated : article.publishDate) as Date, 'dateTime'),
    });
  }

  getId(element: ArticleBody): string {
    return element?.details?.[0]?.value?.id as string;
  }

  setAdMetaAndPageType(articleResponse: Article & ArticleReview): void {
    this.adPageType.set(`column_${articleResponse?.primaryColumn?.slug}`);
    this.adStoreAdo.setArticleParentCategory(this.adPageType());
    this.adStoreAdo.getAdvertisementMeta(articleResponse?.tags, articleResponse?.isAdultsOnly);
  }

  #prepareArticleBody(body: ArticleBody[]): ArticleBody[] {
    let advertIndex = 1;
    return body.map((bodyPart: ArticleBody) => ({
      ...bodyPart,
      details: (bodyPart.details ?? []).map((detail: ArticleBodyDetails) => ({
        ...detail,
        ...this.#prepareArticleBodyDetail(detail, bodyPart.type),
      })),
      ...(bodyPart.type === ArticleBodyType.Advert && {
        adverts: {
          mobile: `mobilinterrupter_${advertIndex}`,
          desktop: `desktopinterrupter_${advertIndex++}`,
        },
      }),
    }));
  }

  private initAds(): void {
    combineLatest([this.route.data.pipe(map(({ data }) => data)), this.adStoreAdo.isAdult.asObservable()])
      .pipe(
        tap(() => this.resetAds()),
        map<
          [
            {
              article: { data: Article & ArticleReview; meta: ApiResponseMeta };
              url?: string;
            },
            boolean,
          ],
          boolean | undefined
        >(([{ article: articleResponse }]) => {
          const articleData = articleResponse.data as Article & ArticleReview;
          this.setAdMetaAndPageType(articleData);
          this.isExceptionAdvertEnabled = (articleResponse as unknown as Article).isExceptionAdvertEnabled;

          return articleData?.withoutAds;
        }),
        switchMap((withoutAds) => {
          withoutAds ? this.adStoreAdo.disableAds() : this.adStoreAdo.enableAds();
          return this.adStoreAdo.advertisemenets$;
        }),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((adverts): void => {
        this.adverts = this.adStoreAdo.separateAdsByMedium(adverts, this.adPageType());
        this.interrupter = this.adStoreAdo.separateAdsByMedium(adverts, this.adPageType());

        this.adStoreAdo.onArticleLoaded();
        this.cdr.detectChanges();
      });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.interrupter = undefined;
    this.cdr.detectChanges();
  }

  #prepareArticleBodyDetail(detail: ArticleBodyDetails, type: ArticleBodyType): ArticleBodyDetails {
    let newDetail: ArticleBodyDetails;
    switch (type) {
      case ArticleBodyType.Article:
        newDetail = { ...detail, value: previewBackendArticleToArticleCard(detail.value) };
        break;
      default:
        newDetail = { ...detail };
    }
    return newDetail;
  }
}

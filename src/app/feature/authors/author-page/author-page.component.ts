import { ChangeDetectionStrategy, Component, computed, inject, OnInit, Signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { map } from 'rxjs/operators';
import { ArticleCard, createCanonicalUrlForPageablePage, LimitableMeta } from '@trendency/kesma-ui';
import {
  ArticleCardComponent,
  ArticleCardType,
  AuthorCardComponent,
  AuthorData,
  AuthorPageData,
  BreadcrumbComponent,
  createBorsOnlineTitle,
  DEFAULT_BREADCRUMB_ITEM,
  defaultMetaInfo,
  makeBreadcrumbSchema,
  PagerComponent,
  PageTitleComponent,
  SearchInputComponent,
  SearchSelectComponent,
} from '../../../shared';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { IMetaData, SchemaOrgService, SeoService } from '@trendency/kesma-core';

@Component({
  selector: 'app-author-page',
  imports: [
    ArticleCardComponent,
    BreadcrumbComponent,
    PageTitleComponent,
    SidebarComponent,
    AuthorCardComponent,
    PagerComponent,
    SearchInputComponent,
    SearchSelectComponent,
  ],
  templateUrl: './author-page.component.html',
  styleUrl: './author-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AuthorPageComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);
  private readonly router = inject(Router);
  private readonly schemaService = inject(SchemaOrgService);

  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data as AuthorPageData)));
  readonly author: Signal<AuthorData | undefined> = computed(() => this.resolverData()?.author);
  readonly articles: Signal<ArticleCard[] | undefined> = computed(() => this.resolverData()?.articles);
  readonly limitable: Signal<LimitableMeta | undefined> = computed(() => this.resolverData()?.limitable);

  readonly ArticleCardType = ArticleCardType;
  readonly orderItems = [
    { value: 'desc', title: 'Legfrissebb elől' },
    { value: 'asc', title: 'Legrégebbi elől' },
  ];

  ngOnInit(): void {
    this.makeBreadCrumbSchema();
    this.setMetaData();
  }

  private makeBreadCrumbSchema(): void {
    const breadcrumbSchema = makeBreadcrumbSchema([DEFAULT_BREADCRUMB_ITEM, { label: 'Szerzők', url: ['/', 'szerzo'] }]);
    this.schemaService.removeStructuredData();
    this.schemaService.insertSchema(breadcrumbSchema);
  }

  get initialOrder(): object {
    return this.orderItems.find(({ value }) => value === (this.route.snapshot.queryParamMap.get('publishDate_order[]') || 'desc')) as object;
  }

  onOrderChanged(orderType: string): void {
    this.router
      .navigate([], {
        queryParams: {
          'publishDate_order[]': orderType,
        },
        queryParamsHandling: 'merge',
      })
      .then();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('szerzo', this.route.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle(this.author()?.publicAuthorName || '');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    };
    this.seoService.setMetaData(metaData);
  }
}

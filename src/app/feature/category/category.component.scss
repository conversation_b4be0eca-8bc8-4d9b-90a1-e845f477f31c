@use 'shared' as *;

:host {
  display: block;
  margin: 32px 0;
  section {
    margin: 20px auto;

    .meta {
      @include media-breakpoint-down(md) {
        padding: 0 16px;
      }
    }

    .wrapper {
      &.with-aside {
        margin-top: 0;
      }
    }
  }

  .main-article {
    padding: 0;
    @include media-breakpoint-down(md) {
      padding: 0 16px;
    }
  }

  .article-list {
    app-article-card {
      margin-bottom: 0;
    }

    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 32px;

    @include media-breakpoint-down(md) {
      gap: 16px;
    }
  }

  .divider {
    background-color: var(--kui-black);
    opacity: 0.2;
    margin: 32px 0;

    &.layout {
      @include media-breakpoint-down(md) {
        margin: 0 16px 32px 16px;
      }
    }
  }

  .page-title {
    color: var(--kui-red-500);
    font-size: 36px;
    font-style: normal;
    font-weight: 800;
    line-height: 42px;
  }

  app-layout ::ng-deep {
    kesma-layout ::ng-deep {
      margin-top: 32px;

      @include media-breakpoint-down(md) {
        margin-top: 16px;
      }
    }

    @include media-breakpoint-down(md) {
      padding: 0 16px;
    }
  }

  app-pager {
    margin: 64px 0;

    @include media-breakpoint-down(md) {
      margin: 32px 0;
    }
  }

  .astronet-branding-box {
    margin-block: -40px 64px;

    @include media-breakpoint-down(md) {
      margin-block: -28px 32px;
    }
  }
}

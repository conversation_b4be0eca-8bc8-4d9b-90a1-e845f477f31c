import { inject, Injectable } from '@angular/core';
import { ApiService, AuthService, SecureApiService } from '../../../shared';
import { ApiListResult, Comment, ReactionsData } from '@trendency/kesma-ui';
import { forkJoin, Observable } from 'rxjs';
import { BackendArticleSocial, CommentableType, CommentRequestParams, HistoricalComment, Reaction } from './comment.definitions';
import { mapBackendCommentToComment, populateReactionsForComments } from '../utils/comment.utils';
import { map, switchMap } from 'rxjs/operators';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class CommentService {
  private readonly publicApi = inject(ApiService);
  private readonly secureApi = inject(SecureApiService);
  private readonly authService = inject(AuthService);
  private readonly router = inject(Router);

  list(type: CommentableType, id: string, params: CommentRequestParams): Observable<ApiListResult<Comment>> {
    return this.authService.isAuthenticated().pipe(
      switchMap((isLoggedIn) => {
        if (!isLoggedIn) {
          return this.publicApi.comments.list(type, id, params).pipe(
            map(({ data, meta }) => ({
              data: data.map(mapBackendCommentToComment),
              meta: {
                ...meta,
                limitable: {},
              },
            }))
          );
        }

        const listQuery$ = this.secureApi.comments.list(type, id, params).pipe(
          map(({ data, meta }) => ({
            data: data.map(mapBackendCommentToComment),
            meta,
          }))
        );

        return forkJoin([listQuery$, this.getUserVotes(type, id)]).pipe(
          map(([commentsResponse, votesResponse]) => populateReactionsForComments(commentsResponse, votesResponse))
        );
      })
    );
  }

  history(latestId: string): Observable<HistoricalComment | undefined> {
    return this.publicApi.comments.history(latestId).pipe(
      map(
        ({ data }) => (data.length ? data[data.length - 1] : undefined) // Last is the original comment
      )
    );
  }

  add(type: 'comment' | 'article', parentId: string, text: string): Observable<void> {
    return this.secureApi.comments.add(type, parentId, text);
  }

  update(id: string, text: string): Observable<void> {
    return this.secureApi.comments.update('comment', id, text);
  }

  clearReaction(id: string): Observable<void> {
    return this.secureApi.comments.clearReaction('comment', id);
  }

  react(id: string, reaction: Reaction): Observable<void> {
    return this.secureApi.comments.vote('comment', id, reaction);
  }

  report(id: string): Observable<void> {
    return this.secureApi.comments.report('comment', id);
  }

  getArticleSocials(articleId: string): Observable<BackendArticleSocial> {
    return this.publicApi.comments.socialData(articleId).pipe(map(({ data }) => data));
  }

  redirectToRegistration(): void {
    this.router
      .navigate(['/', 'regisztracio'], {
        queryParams: { redirect: this.router.routerState.snapshot.url },
      })
      .then();
  }

  redirectToLogin(): void {
    this.router
      .navigate(['/', 'bejelentkezes'], {
        queryParams: { redirect: this.router.routerState.snapshot.url },
      })
      .then();
  }

  private getUserVotes(type: CommentableType, id: string): Observable<ReactionsData> {
    return this.secureApi.comments.getUserVotes(type, id, this.authService.currentUser()!.uid).pipe(map(({ data }) => data));
  }
}

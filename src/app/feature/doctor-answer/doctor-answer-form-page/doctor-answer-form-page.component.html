<section>
  <div class="wrapper">
    <app-breadcrumb [data]="breadcrumbItems"></app-breadcrumb>
    <app-block-title-row [data]="{ text: 'Orvos válaszol' }"></app-block-title-row>
    <hr />
  </div>
  <div class="wrapper with-aside">
    <div class="left-column">
      @if (isOnFirstPage()) {
        <p>
          <b>Kedves Olvasónk!</b><br /><br />
          Köszönjük, hogy felkerested oldalunkat. Kérdésedet itt teheted fel online a Borsonline Napi Doktor szakértőinek. A visszajelzés hamarosan ... napon
          belül felkerül az Orvos Válaszol rovatunkba, ezért érdemes többször visszatérned, hogy biztosan ne maradj le róla! <br />Felhívjuk figyelmedet, hogy a
          szolgáltatás teljesen ingyenes, és a tájékoztatás nem minősül egészségügyi szolgáltatásnak.
          <b>Felhívjuk a figyelmedet, hogy a kérdéseket a válasszal együtt mások is olvashatják, az oldalon közzétételre kerül.</b>
        </p>
        <hr />
        <form [formGroup]="formGroup" (ngSubmit)="handleSubmit()">
          <h2>Kérdés beküldése</h2>
          @if (isLoggedIn()) {
            <app-input-control controlName="subject" labelText="Kérdés címe (kötelező)" placeholder="Adja meg a kérdés címét"></app-input-control>
            <app-input-control controlName="question" labelText="Kérdés (kötelező)" placeholder="Írja ide a szöveget" [useTextArea]="true"></app-input-control>
            <kesma-form-control class="checkbox">
              <label class="bors-form-checkbox centered" for="acceptTerms">
                <input type="checkbox" id="acceptTerms" formControlName="acceptTerms" />
                <span
                  >Tudomásul veszem, hogy a kérdésemre az orvos hamarosan válaszol, és tudomásul veszem, hogy a kérdésemet a válasszal együtt mások is
                  olvashatják, az oldalon közzétételre kerül.</span
                >
              </label>
            </kesma-form-control>
            <app-simple-button [disabled]="isSubmitting()" [isSubmit]="true">{{ isSubmitting() ? 'Kérem várjon...' : 'Beküldés' }}</app-simple-button>
          } @else {
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
              veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
            </p>
            <div class="login-buttons">
              <app-simple-button (keydown)="handleLogin()" (click)="handleLogin()">Bejelentkezés</app-simple-button>
              <app-simple-button (keydown)="handleRegistration()" (click)="handleRegistration()">Regisztráció</app-simple-button>
            </div>
          }
        </form>
        <hr />
      }
      <h2>{{ (currentPage() - 1 ? currentPage() + '. oldal - ' : '') + 'Megválaszolt kérdések' }}</h2>
      <div class="questions">
        @for (question of listItems(); track question.slug) {
          <div class="question">
            <div class="question-date">
              {{ question.publish_date | publishDate: 'yyyy. MM. dd. HH:mm' }}
            </div>
            <div class="question-title">
              <a [routerLink]="['/orvos-valaszol', question.slug]">{{ question.subject }}</a>
            </div>
            <div class="question-question">
              {{ question.question }}
            </div>
            <div class="question-answer">
              {{ question.answer || '' | limitString: 53 }}
            </div>
            @if (question.slug) {
              <a [routerLink]="['/orvos-valaszol', question.slug]" class="question-button">
                Tovább a válaszra <kesma-icon name="arrow-right-long" size="16"></kesma-icon
              ></a>
            }
          </div>
        }
      </div>
      @if (limitable()?.pageMax) {
        <app-pager [rowAllCount]="limitable()?.rowAllCount" [rowOnPageCount]="limitable()?.rowOnPageCount"></app-pager>
      }
    </div>
    <aside>
      <app-sidebar />
    </aside>
  </div>
</section>

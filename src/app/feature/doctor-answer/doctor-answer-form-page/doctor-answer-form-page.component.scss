@use 'shared' as *;

@use '@angular/cdk' as cdk;

@include cdk.overlay();

::ng-deep {
  .cdk-overlay-container {
    z-index: 2000;
  }
  .orvos-backdrop {
    background-color: rgba(0, 0, 0, 0.75);
  }
  .form-error {
    font-size: 12px !important;
    font-weight: 600 !important;
  }
}

:host {
  display: block;
  margin-block: 32px;

  @include media-breakpoint-down(md) {
    padding-inline: 16px;
  }
  .wrapper {
    padding: 0;
  }
  .with-aside {
    gap: 24px;
    margin-top: 0;
  }
  hr {
    margin-top: 32px;
    margin-bottom: 32px;
    @include media-breakpoint-down(md) {
      margin-top: 16px;
      margin-bottom: 16px;
    }
  }
  app-simple-button {
    margin: 0;
  }
  app-breadcrumb {
    ::ng-deep {
      ul.breadcrumb {
        display: block;
        li {
          display: inline;
        }
      }
    }
  }
  .left-column {
    h2 {
      font-size: 36px;
      font-style: normal;
      font-weight: 800;
      line-height: 42px;
      @include media-breakpoint-down(md) {
        font-size: 26px;
        line-height: 28px;
      }
    }
    p {
      font-size: 20px;
      line-height: 28px; /* 140% */
      margin-bottom: 40px;
      @include media-breakpoint-down(md) {
        font-size: 18px;
        line-height: 26px;
      }
    }
    form {
      display: flex;
      flex-direction: column;
      gap: 32px;
      p {
        margin: 0;
      }
      .login-buttons {
        display: flex;
        gap: 16px;
        justify-content: center;
      }
      .bors-form-checkbox:before {
        top: 0;
      }
      .bors-form-checkbox.centered input:checked + span:before {
        top: 0;
        margin-top: 0;
      }
    }
    .questions {
      margin-top: 40px;
    }
    .question {
      display: flex;
      flex-direction: column;
      gap: 10px;
      &:after {
        content: '';
        width: 100%;
        height: 1px;
        background-color: var(--kui-black-200);
        margin-top: 32px;
        margin-bottom: 32px;
      }
      &-date {
        font-size: 14px;
      }
      &-title {
        font-size: 20px;
        font-weight: 700;
        line-height: 24px; /* 120% */
        @include media-breakpoint-down(md) {
          font-size: 18px;
          line-height: 22px;
        }
      }
      &-question {
        font-size: 18px;
        line-height: 26px; /* 144.444% */
        font-weight: 600;
        @include media-breakpoint-down(md) {
          font-size: 16px;
          line-height: 24px;
        }
      }
      &-answer {
        font-size: 18px;
        line-height: 26px; /* 144.444% */
        @include media-breakpoint-down(md) {
          font-size: 14px;
          line-height: 24px;
        }
      }
      &-button {
        display: flex;
        gap: 10px;
        font-size: 16px;
        font-weight: 700;
        line-height: 20px; /* 125% */
        color: var(--kui-red-500);
      }
    }
  }
  app-social-share {
    margin-block: 32px;
  }
}

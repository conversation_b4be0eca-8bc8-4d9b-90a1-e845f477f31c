import { ChangeDetectionStrategy, Component, computed, effect, inject } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { BreadcrumbItem, createCanonicalUrlForPageablePage, IconComponent } from '@trendency/kesma-ui';
import { BreadcrumbComponent, createBorsOnlineTitle, defaultMetaInfo, makeBreadcrumbSchema } from '../../../shared';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { BlockTitleRowComponent } from '../../../shared';
import { PublishDatePipe, SchemaOrgService, SeoService } from '@trendency/kesma-core';
import { Dialog } from '@angular/cdk/dialog';
import { ReactiveFormsModule } from '@angular/forms';
import { toSignal } from '@angular/core/rxjs-interop';
import { DoctorAnswerDetail } from '../doctor-answer.definitions';

@Component({
  selector: 'app-doctor-answer-question-page',
  templateUrl: './doctor-answer-question-page.component.html',
  styleUrl: './doctor-answer-question-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SidebarComponent, BlockTitleRowComponent, BreadcrumbComponent, ReactiveFormsModule, RouterLink, IconComponent, PublishDatePipe],
  providers: [Dialog],
})
export class DoctorAnswerQuestionPageComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);
  private readonly schemaService = inject(SchemaOrgService);
  readonly routeData = toSignal(this.route.data);
  readonly detail = computed<DoctorAnswerDetail>(() => this.routeData()?.['data'].data);

  readonly breadcrumbItems = computed<BreadcrumbItem[]>(() => {
    const detail = this.detail();
    return [
      {
        label: 'Orvos válaszol',
        url: '/orvos-valaszol',
      },
      {
        label: detail.subject,
      },
    ] as BreadcrumbItem[];
  });

  constructor() {
    effect(() => {
      const breadcrumbItems = this.breadcrumbItems();
      const breadcrumbSchema = makeBreadcrumbSchema(breadcrumbItems);
      this.schemaService.insertSchema(breadcrumbSchema);
      this.setMetaData();
    });
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage(`orvos-valaszol`, this.route.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle(this.detail().subject + ' - Orvos válaszol');
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    });
  }
}

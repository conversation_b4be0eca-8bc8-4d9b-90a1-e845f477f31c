import { Routes } from '@angular/router';
import { PageValidatorGuard } from '@trendency/kesma-ui';
import { DoctorAnswerFormPageComponent } from './doctor-answer-form-page/doctor-answer-form-page.component';
import { doctorAnswerFormPageResolver } from './doctor-answer-form-page/doctor-answer-form-page.resolver';
import { DoctorAnswerQuestionPageComponent } from './doctor-answer-question-page/doctor-answer-question-page.component';
import { doctorAnswerQuestionPageResolver } from './doctor-answer-question-page/doctor-answer-question-page.resolver';

export const DOCTOR_ANSWER_ROUTES: Routes = [
  {
    path: '',
    component: DoctorAnswerFormPageComponent,
    canActivate: [PageValidatorGuard],
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    pathMatch: 'full',
    resolve: {
      data: doctorAnswerFormPageResolver,
    },
  },
  {
    path: ':slug',
    component: Doctor<PERSON>nswerQuestionPageComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    pathMatch: 'full',
    resolve: {
      data: doctorAnswerQuestionPageResolver,
    },
  },
];

@use 'shared' as *;

:host {
  display: block;
  margin-block: 32px;
  .divider {
    background-color: var(--kui-black);
    opacity: 0.2;
    margin-block: 32px;
    @include media-breakpoint-down(md) {
      margin-block: 16px;
    }
  }
  .with-aside {
    margin-top: 0;
  }
  .article-card {
    margin-bottom: 32px;
    &:last-of-type {
      margin-bottom: 0;
    }
    @include media-breakpoint-down(md) {
      margin-bottom: 16px;
    }
  }
  .page-title::ng-deep h1 {
    color: var(--kui-dark-blue-800);
  }
  .pager {
    margin-top: 64px;
    @include media-breakpoint-down(md) {
      margin-block: 32px 17px;
    }
  }
  aside {
    margin-bottom: 0;
  }
}

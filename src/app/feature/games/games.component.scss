@use 'shared' as *;

$gameBackgroundColor: #0182a9;
$gameBackgroundColorSecondary: #fe3c3c;
$gameBackgroundColorTertiary: #143c6c;
$gameTextColor: #3f3f3f;
$adBackgroundColor: #ebeaea;

:host {
  height: 100%;
  display: flex;
  margin: 32px 0;
  flex-direction: column;
  gap: 32px;
  max-width: 1376px;
}

.content-wrapper {
  background: $gameBackgroundColor;

  @include media-breakpoint-down(sm) {
    .wrapper {
      padding: 0;
    }
  }
}

.games-content {
  background: url('/assets/images/games.jpg') 50% 0 no-repeat;
  background-size: contain;
  width: 100%;
  max-width: 1920px;
  margin: auto;
  min-height: 1300px;
  padding-bottom: 32px;

  @include media-breakpoint-down(sm) {
    padding-bottom: 0;
    margin-bottom: 10px;
  }
}

.games-head {
  height: 110px;

  @include media-breakpoint-down(lg) {
    height: 90px;
  }

  @include media-breakpoint-down(md) {
    height: 75px;
  }

  @include media-breakpoint-down(sm) {
    height: 55px;
  }

  @include media-breakpoint-down(xs) {
    height: 40px;
  }
}
.game-nav {
  transform: translate(5px, 41px);
  @include media-breakpoint-up(md) {
    transform: translate(5px, 26px);
  }
  .game-logo {
    display: block;
    width: 237px;
    height: 68px;
  }
  .game-kvizpart {
    background: $gameBackgroundColorTertiary;
    display: inline-block;
    font-family: var(--kui-font-primary);
    font-size: 16px;
    padding: 5px 10px;
    text-transform: uppercase;
    transform: translateY(10px);
    color: var(--kui-white);
  }
}
.related-game {
  border: 5px solid $gameBackgroundColorTertiary;
  position: relative;
  margin: 0 32px;

  @include media-breakpoint-down(md) {
    margin: 0 16px;
  }

  ::ng-deep {
    .advertisement-block {
      display: block;
    }
    img {
      display: block;
      height: auto;
      width: 100%;
    }
    p,
    .tags {
      display: none;
    }
    .jatek-cim {
      background: var(--kui-black);
      color: var(--kui-white);
      display: block;
      font-family: var(--kui-font-primary);
      font-size: 18px;
      font-weight: 700;
      padding: 15px;
      text-transform: uppercase;
      width: 100%;
      @include media-breakpoint-up(sm) {
        background: rgb(0 0 0 / 70%);
        bottom: 0;
        font-size: 20px;
        left: 0;
        position: absolute;
      }
      @include media-breakpoint-up(md) {
        font-size: 40px;
        padding: 30px;
      }
    }
  }
}
.games-lb {
  background: var(--kui-white);
  margin: 0 32px;

  @include media-breakpoint-down(md) {
    margin: 0 16px;
  }
}
.list-of-games {
  background: var(--kui-white);
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
  padding: 10px;
  margin: 0 32px;

  @include media-breakpoint-down(md) {
    margin: 0 16px;
  }

  @include media-breakpoint-up(sm) {
    grid-template-columns: repeat(2, 1fr);
  }
  @include media-breakpoint-up(md) {
    grid-template-columns: repeat(3, 1fr);
  }
  @include media-breakpoint-up(xl) {
    grid-template-columns: repeat(4, 1fr);
  }
  ::ng-deep {
    kesma-advertisement-adocean {
      background: $adBackgroundColor;
      position: relative;
      &.empty-zone {
        display: none;
        visibility: hidden;
      }
      section,
      .ad-wrapper,
      .advertisement-block {
        height: 100%;
      }
    }
    .jatek-elem {
      padding: 0 0 30px;
      height: 100%;
      position: relative;
      img {
        display: block;
        height: auto;
        width: 100%;
      }
      .jatek-cim {
        font-family: var(--kui-font-primary);
        font-weight: 700;
        color: $gameTextColor;
        display: block;
        font-size: 21px;
        padding: 6px 12px;
      }
      p {
        border-top: 1px dashed rgb(0 0 0 / 25%);
        color: $gameTextColor;
        font-size: 14px;
        line-height: 18px;
        padding: 6px 12px;
      }
      .tags,
      .game-play {
        font-family: var(--kui-font-primary);
        font-weight: 700;
        font-size: 14px;
      }
      .tags {
        bottom: 0;
        left: 0;
        position: absolute;
        span {
          display: none;
          &.game-type {
            background: $gameBackgroundColorSecondary;
            color: var(--kui-white);
            display: inline-block;
            padding: 5px;
            text-transform: uppercase;
            &:empty {
              visibility: hidden;
            }
          }
        }
      }
      .game-play {
        background: $gameBackgroundColor;
        bottom: 0;
        color: var(--kui-white);
        display: inline-block;
        padding: 5px;
        position: absolute;
        right: 0;
        text-transform: uppercase;
      }
    }
  }
}

import { AfterViewInit, Component, inject, OnDestroy } from '@angular/core';
import { ActivatedRoute, Data } from '@angular/router';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { AnalyticsService, LayoutApiData, LayoutPageType } from '@trendency/kesma-ui';
import { map, Observable } from 'rxjs';
import { defaultMetaInfo } from '../../shared';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { AsyncPipe } from '@angular/common';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  imports: [LayoutComponent, AsyncPipe],
})
export class HomeComponent implements AfterViewInit, OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly seo = inject(SeoService);
  private readonly utilService = inject(UtilService);
  private readonly analyticsService = inject(AnalyticsService);

  private links: HTMLCollectionOf<HTMLAnchorElement>;

  layoutApiData$: Observable<LayoutApiData> = this.route.data.pipe(
    map((data: Data | { layoutData: LayoutApiData }) => {
      this.seo.setMetaData({
        ...defaultMetaInfo,
        keywords: 'hírek, információk, sporthírek, sztárok, életmód, időjárás, programajánló',
      });
      return data.layoutData;
    })
  );

  LayoutPageType = LayoutPageType;

  constructor() {
    this.seo.updateCanonicalUrl('');
  }

  ngAfterViewInit(): void {
    if (!this.utilService.isBrowser()) {
      return;
    }

    queueMicrotask(() => {
      this.links = document.getElementsByTagName('a');
      Array.from(this.links ?? []).forEach((element) => {
        element.addEventListener('click', this.handleAnchorClickEvent);
        element.addEventListener('auxclick', this.handleAnchorClickEvent);
      });
    });
  }

  ngOnDestroy(): void {
    if (!this.utilService.isBrowser()) {
      return;
    }

    Array.from(this.links ?? []).forEach((element) => {
      element.removeEventListener('click', this.handleAnchorClickEvent);
      element.removeEventListener('auxclick', this.handleAnchorClickEvent);
    });
  }

  handleAnchorClickEvent = (ev: MouseEvent): void => {
    const link = (ev.composedPath().find((elem: EventTarget) => (elem as Element).nodeName === 'A') as HTMLAnchorElement)?.href;
    if (link) {
      this.analyticsService.sendMainPageClick(link, document.referrer);
    }
  };
}

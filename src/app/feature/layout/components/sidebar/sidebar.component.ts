import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  inject,
  input,
  OnChanges,
  OnInit,
  output,
  signal,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { EmbeddingService, UtilService } from '@trendency/kesma-core';
import { Layout, LayoutArticle, LayoutContent, LayoutElementRow, LayoutPageType, LayoutService } from '@trendency/kesma-ui';
import { forkJoin, of } from 'rxjs';
import { catchError, mergeMap, tap } from 'rxjs/operators';
import { LayoutComponent } from '../layout/layout.component';
import { environment } from '../../../../../environments/environment';
import { ApiService } from '../../../../shared';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
  imports: [LayoutComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SidebarComponent implements OnInit, OnChanges, AfterViewInit {
  private readonly api = inject(ApiService);
  private readonly layoutService = inject(LayoutService);
  private readonly embedding = inject(EmbeddingService);
  private readonly utilsService = inject(UtilService);
  private readonly cdr = inject(ChangeDetectorRef);

  adPageType = input<string>('');
  categorySlug = input<string | undefined>();
  articleId = input<string | undefined>();
  articleSlug = input<string | undefined>();
  columnSlug = input<string | undefined>();
  excludedIds = input<string[]>([]);
  populatedEvent = output<boolean>();
  @ViewChild('layout') layoutComponent: LayoutComponent;
  layoutApiData = signal<Layout | undefined>(undefined);
  layoutReady = signal(false);

  LayoutPageType = LayoutPageType;

  ngOnInit(): void {
    this.populateSidebar();
  }

  structure = computed<LayoutElementRow[]>(() => {
    return this.layoutApiData()?.struct as LayoutElementRow[];
  });

  populateSidebar(): void {
    const articleId = this.articleId();
    const allExcludedId: string[] = [...(articleId?.length ? [articleId] : []), ...this.excludedIds()];

    this.layoutService
      .getSidebar(this.categorySlug(), this.articleSlug())
      .pipe(
        tap(() => {
          this.layoutApiData.set(undefined);
          this.cdr.detectChanges();
        }),
        mergeMap((layoutApiResponse) =>
          forkJoin([
            of(layoutApiResponse),
            this.api.getSidebarArticleRecommendations(this.getArticleCount(layoutApiResponse?.data?.content), this.categorySlug(), allExcludedId).pipe(
              catchError((error, _) => {
                if (!environment.production && this.utilsService.isBrowser()) {
                  console.warn('[dev] Unable to download sidebar: ', error);
                }
                return [];
              })
            ),
          ])
        )
      )
      .subscribe(([{ data: layoutApiData }, { data: recommendedArticles }]) => {
        this.layoutApiData.set(layoutApiData);
        this.fillLayoutContent(recommendedArticles as LayoutArticle[], []);
        this.layoutReady.set(true);
        setTimeout(() => {
          this.embedding.loadEmbedMedia();
        }, 0);
        this.populatedEvent.emit(true);
        this.cdr.detectChanges();
      });
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.embedding.loadEmbedMedia();
    }, 0);
  }

  ngOnChanges(changes: SimpleChanges): void {
    // need sidebar refetch to avoid showing the same article in sidebar that is currently displayed on detail page
    if (
      (changes['articleId']?.currentValue && !changes['articleId']?.firstChange) ||
      (changes['excludedIds']?.currentValue && !changes['excludedIds']?.firstChange)
    ) {
      this.populateSidebar();
    }
  }

  getArticleCount(content: LayoutContent[]): number {
    // no flat() ??? -> upgrade to es2019
    return content.map(({ selectedArticles }) => selectedArticles).reduce((acc, val) => acc.concat(val), []).length;
  }

  getOpinionCount(content: LayoutContent[]): number {
    // no flat() ??? -> upgrade to es2019
    return content.map(({ selectedOpinions }) => selectedOpinions).reduce((acc, val) => acc.concat(val), []).length;
  }

  fillLayoutContent(articles: LayoutArticle[], opinions: LayoutArticle[]): void {
    let articleCursor = 0;
    let opinionCursor = 0;
    this.layoutApiData()?.content.forEach(({ selectedArticles, selectedOpinions }) => {
      if (selectedArticles?.length) {
        for (let i = 0; i < selectedArticles.length; i++) {
          // only overwrite null values
          if (!selectedArticles[i] && articles[articleCursor]) {
            selectedArticles[i] = {
              id: articles[articleCursor]?.id ?? '',
              data: articles[articleCursor],
            };
            articleCursor++;
          }
        }
      } else if (selectedOpinions?.length) {
        for (let i = 0; i < selectedOpinions.length; i++) {
          // only overwrite null values
          if (!selectedOpinions[i] && opinions[opinionCursor]) {
            selectedOpinions[i] = {
              id: opinions[opinionCursor]?.id ?? '',
              data: opinions[opinionCursor],
            };
            opinionCursor++;
          }
        }
      }
    });
  }
}

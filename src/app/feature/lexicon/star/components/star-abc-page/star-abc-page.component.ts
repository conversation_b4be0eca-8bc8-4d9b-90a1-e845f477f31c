import { NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, createCanonicalUrlForPageablePage, Limitable } from '@trendency/kesma-ui';
import { map, switchMap } from 'rxjs/operators';
import { DEFAULT_BREADCRUMB_ITEM, makeBreadcrumbSchema, PagerComponent } from '../../../../../shared';
import { LexiconMetaService } from '../../../api/lexicon-meta.service';
import { LexiconBreadcrumbComponent } from '../../../components/lexicon-breadcrumb/lexicon-breadcrumb.component';
import { IStarAbc } from '../../definitions/star-abc.definitions';
import { IStarCard, StarCardTypes } from '../../definitions/star.definitions';
import { StarCardComponent } from '../star-card/star-card.component';
import { StarWrapperComponent } from '../star-wrapper/star-wrapper.component';
import { StarAbcFilterComponent } from './components/star-abc-filter/star-abc-filter.component';
import { SchemaOrgService, SeoService } from '@trendency/kesma-core';

@Component({
  selector: 'app-star-abc-page',
  imports: [
    LexiconBreadcrumbComponent,
    StarCardComponent,
    StarWrapperComponent,
    StarAbcFilterComponent,
    PagerComponent,
    NgIf,
    RouterLink,
    AdvertisementAdoceanComponent,
  ],
  templateUrl: './star-abc-page.component.html',
  styleUrl: './star-abc-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StarAbcPageComponent implements OnInit {
  private readonly breadcrumbService = inject(LexiconMetaService);
  private readonly route = inject(ActivatedRoute);
  private readonly destroyRef = inject(DestroyRef);
  private readonly adStore = inject(AdvertisementAdoceanStoreService);
  private readonly seoService = inject(SeoService);
  private readonly schemaService = inject(SchemaOrgService);

  readonly ads = toSignal(this.adStore.advertisemenets$.pipe(map((ads) => this.adStore.separateAdsByMedium(ads))));

  readonly StarCardTypes = StarCardTypes;

  starAbc: IStarAbc[] = [];
  breadcrumbItems = toSignal(this.route.data.pipe(switchMap((res) => this.breadcrumbService.getStarPageBreadcrumbs(res['data'].activeLetter))));
  activeLetter: string;
  stars: IStarCard[] = [];
  limitable: Limitable;

  ngOnInit(): void {
    this.route.data.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res) => {
      const data = res['data'];
      this.starAbc = data.starAbc;
      this.stars = data.data?.data;
      this.limitable = data.data?.meta?.limitable;
      this.activeLetter = data.activeLetter;
      this.setCanonicalUrl(data.activeLetter);
      this.handleSchema();
    });
  }

  private setCanonicalUrl(activeLetter: string): void {
    const canonical = createCanonicalUrlForPageablePage(`lexikon/sztar/abc/${activeLetter}`, this.route.snapshot);
    this.seoService.updateCanonicalUrl(canonical);
  }

  private handleSchema(): void {
    this.schemaService.removeStructuredData();
    this.schemaService.insertSchema(makeBreadcrumbSchema([DEFAULT_BREADCRUMB_ITEM, ...(this.breadcrumbItems() ?? [])]));
  }
}

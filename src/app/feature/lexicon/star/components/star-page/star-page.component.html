<app-star-wrapper [leftContent]="leftContent" [isStarMainPage]="true"></app-star-wrapper>
<ng-template #leftContent>
  <app-lexicon-breadcrumb [items]="breadcrumbItems()"></app-lexicon-breadcrumb>
  <ng-container *ngTemplateOutlet="mainStarPage"></ng-container>
</ng-template>

<!-- MAIN PAGE -->

<ng-template #mainStarPage>
  @for (starBox of stars(); track starBox.starBoxType) {
    @switch (starBox.starBoxType) {
      <!-- DAILY STAR -->
      @case (MainPageStarBoxTypesEnum.DAILY_STAR) {
        @if (randomDailyStar()) {
          <h2 class="daily-star-title">A nap sztárja</h2>
          <app-star-card class="daily-star-card" [data]="randomDailyStar()" [styleId]="StarCardTypes.FEATURED"></app-star-card>
        }
        <app-star-search></app-star-search>

        <!-- ADVERTISEMENT -->
        <kesma-advertisement-adocean
          [style]="{ margin: 'var(--ad-margin)' }"
          [hasNoParentHeight]="true"
          *ngIf="adverts()?.desktop?.roadblock_1 as ad"
          [ad]="ad"
        ></kesma-advertisement-adocean>
        <kesma-advertisement-adocean
          [style]="{ margin: 'var(--ad-margin)' }"
          [hasNoParentHeight]="true"
          *ngIf="adverts()?.mobile?.mobilrectangle_1 as ad"
          [ad]="ad"
        ></kesma-advertisement-adocean>
      }
      @default {
        <!-- STAR RELATED BOXES -->
        <ng-container *ngTemplateOutlet="relatedStarBox; context: { starBoxData: starBox }"></ng-container>
      }
    }
  }
  <!-- ADVERTISEMENT -->
  <kesma-advertisement-adocean
    [style]="{ margin: 'var(--ad-margin)' }"
    [hasNoParentHeight]="true"
    *ngIf="adverts()?.desktop?.roadblock_2 as ad"
    [ad]="ad"
  ></kesma-advertisement-adocean>
  <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [hasNoParentHeight]="true" *ngIf="adverts()?.mobile?.mobilrectangle_2 as ad" [ad]="ad">
  </kesma-advertisement-adocean>
</ng-template>

<ng-template #relatedStarBox let-starBoxData="starBoxData">
  @if (starBoxData?.starData?.length) {
    <app-star-link
      [isStarMainPage]="true"
      class="star-box-header-link"
      [data]="{ text: starBoxData?.title, url: starBoxData?.url }"
      [styleId]="StarLinkTypes.LINE_RIGHT"
    ></app-star-link>
    <app-star-box [data]="starBoxData?.starData"></app-star-box>
    <app-star-link
      class="star-box-more-stars-link"
      [styleId]="StarLinkTypes.LINE_LEFT"
      [data]="{ text: 'TOVÁBBI SZTÁROK', url: starBoxData?.url }"
    ></app-star-link>
  }
</ng-template>

@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  .star-header {
    background-color: var(--kui-red-500);
    position: absolute;
    width: 100vw;
    top: 100%;
    img {
      width: 100%;
    }
  }

  .header {
    position: relative;

    &-wrapper {
      width: 100vw;
      position: relative;
      left: 50%;
      right: 50%;
      margin-left: -50vw;
      margin-right: -50vw;
      padding-top: 26px;
      background: linear-gradient(to top, $red-500, $dark-red);
      padding-bottom: 52px;
    }

    &-title {
      color: $white;
      font-size: 30px;
      font-weight: 700;
      line-height: 37px;
      letter-spacing: 0.9px;
      text-transform: uppercase;
    }
  }

  .wrapper {
    @media screen and (min-width: 1501px) {
      max-width: 1370px;
    }

    @media screen and (max-width: 1500px) {
      padding: 0px 70px;
    }

    @include media-breakpoint-down(md) {
      padding: 0 21px;
    }

    @include media-breakpoint-down(sm) {
      padding: 0 15px;
    }
  }
}

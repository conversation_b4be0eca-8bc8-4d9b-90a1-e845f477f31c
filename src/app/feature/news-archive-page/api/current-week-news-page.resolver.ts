import { ResolveFn } from '@angular/router';
import { inject } from '@angular/core';
import { NewsArchiveService } from './news-archive-page.service';
import { ArchiveResponse, GroupedArchiveArticlesByColumn, NewsArchiveArticleColumns } from '../definitions/news-archive-page.definitions';
import { ApiResponseMetaList } from '@trendency/kesma-ui';

export const CurrentWeekNewsResolver: ResolveFn<ArchiveResponse<GroupedArchiveArticlesByColumn[], ApiResponseMetaList, NewsArchiveArticleColumns[]>> = () => {
  const newsArchiveService = inject(NewsArchiveService);
  return newsArchiveService.getCurrentWeekArticles$();
};

import { Injectable } from '@angular/core';
import { map, Observable, of } from 'rxjs';
import { ApiResponseMetaList, ApiResult, BackendArticleSearchResult } from '@trendency/kesma-ui';
import { ApiService, backendArticlesSearchResultsToArticleSearchResArticles } from '../../../shared';
import { ArchiveResponse, GroupedArchiveArticlesByColumn, NewsArchiveArticleColumns, NewsArchiveTimeLine } from '../definitions/news-archive-page.definitions';
import { endOfWeek, parse, set, startOfWeek } from 'date-fns';
import { formatInTimeZone, fromZonedTime } from 'date-fns-tz';

const ROW_COUNT_LIMIT = 100;
const ROW_COUNT_LIMIT_FOR_WEEK = 1000;

const FIRST_YEAR: NewsArchiveTimeLine = {
  year: 1998,
  months: [
    {
      month: '11',
      date: new Date(`1998-11-01T00:00:00Z`),
    },
    {
      month: '12',
      date: new Date(`1998-12-01T00:00:00Z`),
    },
  ],
};

@Injectable({ providedIn: 'root' })
export class NewsArchiveService {
  constructor(private readonly apiService: ApiService) {}

  getNewsArchiveMonth$(): Observable<NewsArchiveTimeLine[]> {
    const actualMonth = +(new Date().getMonth() + 1);
    const actualYear = +new Date().getFullYear();

    const timelineData: NewsArchiveTimeLine[] = [];
    timelineData.push(FIRST_YEAR);

    for (let i = 1999; i <= actualYear; i++) {
      const currentYearMonths = i === actualYear ? actualMonth : 12;
      timelineData.push({ year: i, months: [] });
      for (let j = 1; j <= currentYearMonths; j++) {
        const index = timelineData.findIndex((data) => data.year === i);
        const date = `${i}-${('0' + j).slice(-2)}-01T00:00:00Z`;
        timelineData[index].months?.push({
          month: j < 10 ? `0${j}` : j.toString(),
          date: new Date(date),
        });
      }
    }

    return of(timelineData.reverse());
  }

  getArticlesByDate$(
    year: string,
    month: string,
    day?: string
  ): Observable<ArchiveResponse<GroupedArchiveArticlesByColumn[], ApiResponseMetaList, NewsArchiveArticleColumns[]>> {
    const searchDate: string = day ? `${year}-${month}-${day}` : `${year}-${month}-01`;
    const searchDateAsDate: Date = parse(searchDate, 'yyyy-MM-dd', new Date());
    const fixedSearchDate = this.handleServerTimeInterval(searchDateAsDate, searchDateAsDate);

    const extraParams: Record<string, string | string[]> = {
      from_date: fixedSearchDate.fromDate,
      to_date: fixedSearchDate.toDate,
      'content_types[]': ['article'],
    };
    return this.apiService.searchByParams(0, ROW_COUNT_LIMIT, extraParams).pipe(
      map(({ data, meta }: ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>) => {
        return {
          articles: this.groupArticlesByColumn(data),
          meta,
          columns: this.getArticleColums(data),
        };
      })
    );
  }

  getCurrentWeekArticles$(): Observable<ArchiveResponse<GroupedArchiveArticlesByColumn[], ApiResponseMetaList, NewsArchiveArticleColumns[]>> {
    const today = new Date();
    const weekStart = startOfWeek(today, { weekStartsOn: 1 });
    const weekEnd = endOfWeek(today, { weekStartsOn: 1 });
    const fixedSearchDate = this.handleServerTimeInterval(weekStart, weekEnd);
    const extraParams: Record<string, string | string[]> = {
      from_date: fixedSearchDate.fromDate,
      to_date: fixedSearchDate.toDate,
      'content_types[]': ['article'],
    };
    return this.apiService.searchByParams(0, ROW_COUNT_LIMIT_FOR_WEEK, extraParams).pipe(
      map(({ data, meta }: ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>) => {
        return {
          articles: this.groupArticlesByColumn(data),
          meta,
          columns: this.getArticleColums(data),
        };
      })
    );
  }

  groupArticlesByColumn(articles: BackendArticleSearchResult[]): GroupedArchiveArticlesByColumn[] {
    const resultArray: GroupedArchiveArticlesByColumn[] = [];
    articles.map((archiveArticle) => {
      if (!resultArray.find((category) => category?.column?.columnSlug === archiveArticle?.columnSlug)) {
        resultArray.push({
          column: {
            columnSlug: archiveArticle?.columnSlug || '',
            columnTitle: archiveArticle?.columnTitle || '',
          },
          articles: [
            ...articles
              .filter((article) => article?.columnSlug === archiveArticle?.columnSlug)
              .map(backendArticlesSearchResultsToArticleSearchResArticles)
              .sort((a, b) => {
                const date = new Date(a?.publishDate);
                const date2 = new Date(b?.publishDate);
                if (date < date2) {
                  return -1;
                } else if (date > date2) {
                  return 1;
                }
                return 0;
              }),
          ],
        });
      }
    });
    return resultArray;
  }

  getArticleColums(data: BackendArticleSearchResult[]): NewsArchiveArticleColumns[] {
    const resultArray: NewsArchiveArticleColumns[] = [];
    data.map((article) => {
      if (!resultArray.find((resultArticle) => resultArticle.columnSlug === article.columnSlug)) {
        resultArray.push({ columnSlug: article.columnSlug, columnTitle: article.columnTitle });
      }
    });
    return resultArray;
  }

  private handleServerTimeInterval(fromDate: Date, toDate: Date): { fromDate: string; toDate: string } {
    const startDate: Date = set(fromDate, {
      hours: 0,
      minutes: 0,
      seconds: 0,
      milliseconds: 0,
    });

    const endDate: Date = set(toDate, {
      hours: 23,
      minutes: 59,
      seconds: 59,
      milliseconds: 999,
    });

    const timeZone = 'Europe/Budapest';

    const fromUTC = fromZonedTime(startDate, timeZone);
    const toUTC = fromZonedTime(endDate, timeZone);

    const backendDateFormat = `yyyy-MM-dd'T'HH:mm:ss`;

    return {
      fromDate: formatInTimeZone(fromUTC, 'UTC', backendDateFormat),
      toDate: formatInTimeZone(toUTC, 'UTC', backendDateFormat),
    };
  }
}

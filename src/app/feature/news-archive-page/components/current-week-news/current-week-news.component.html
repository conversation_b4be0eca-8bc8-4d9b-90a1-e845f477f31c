<section>
  <div class="wrapper content-wrapper">
    <h1 class="archive-title">HÍRARCHÍVUM</h1>
    <div class="date-picker-wrapper">
      @if (currentWeekArticles$ | async; as weekArticles) {
        @if (weekArticles?.articles?.length) {
          <div class="columns-container">
            @for (column of weekArticles?.columns; track column.columnSlug) {
              <a class="columns-container-title" [routerLink]="getCategoryLink(column)">{{ column?.columnTitle }}</a>
            }
          </div>
          @for (columnTypes of weekArticles?.articles; track columnTypes?.column?.columnSlug) {
            @if (columnTypes?.column; as articleColumn) {
              <div class="article-column">
                <a [routerLink]="['/', 'rovat', articleColumn?.columnSlug]">{{ articleColumn?.columnTitle }}</a>
              </div>
            }
            <div class="categorized-article-container">
              @for (article of columnTypes?.articles; track article?.id) {
                <article>
                  <a [routerLink]="getArticleLink(article)">
                    <span class="categorized-article-container-publish-date">{{ article?.publishDate | archiveArticlePublishDatePipe }}</span>
                    <h2 class="categorized-article-container-title">{{ article?.title }}</h2>
                  </a>
                </article>
              }
            </div>
          }
        } @else {
          <div class="no-article-result-container">
            <h2>Az adott hétre vontakozóan nincs találat!</h2>
          </div>
        }
      }
    </div>
  </div>
</section>

@use 'shared' as *;

$grey: #ddd;

:host {
  display: block;
  margin: 48px 0;

  .content-wrapper {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0.5px solid $grey;
    width: 1160px;
    @include media-breakpoint-down(md) {
      max-width: calc(100% - 40px) !important;
    }
  }

  .archive-title {
    color: var(--kui-red-500);
    font-size: 30px;
    padding: 20px;
    border-bottom: 0.5px solid $grey;

    @include media-breakpoint-down(sm) {
      text-align: center;
    }
  }

  .date-picker-wrapper {
    width: fit-content;
    border-right: 1px solid $grey;
  }

  .no-article-result-container {
    padding: 20px;
    text-align: center;

    h2 {
      display: inline;
    }
  }

  .columns-container {
    display: flex;
    flex-direction: row;
    padding: 6px 10px;
    flex-wrap: wrap;
    border-bottom: 1px solid $grey;
    gap: 10px;

    &-title {
      color: var(--kui-red-500);
      font-size: 14px;
      font-weight: 600;
      text-transform: uppercase;
      transition: color 0.3s ease;
      display: flex;
      align-items: center;

      &:not(:last-child) {
        &::after {
          background-color: #bbb;
          border-radius: 50%;
          content: '';
          display: inline-block;
          height: 4px;
          margin-left: 8px;
          width: 4px;
        }
      }
    }
  }

  .article-column {
    border-bottom: 1px solid $grey;
    padding: 8px 10px;

    a {
      text-transform: uppercase;
      font-size: 28px;
      font-weight: 700;
      line-height: 1;
      color: var(--kui-red-500);
    }
  }

  .categorized-article-container {
    display: flex;
    flex-direction: column;
    padding: 10px;
    gap: 30px;

    &:not(:last-child) {
      border-bottom: 1px solid $grey;
    }

    &-publish-date {
      color: #999;
      font-size: 14px;
      font-weight: 700;
      text-transform: uppercase;

      @include media-breakpoint-down(sm) {
        font-size: 12px;
      }
    }

    &-title {
      font-size: 30px;
      font-weight: 600;
      line-height: 33px;
      color: var(--kui-black);

      @include media-breakpoint-down(sm) {
        font-size: 24px;
      }
    }
  }
}

import { ChangeDetectionStrategy, Component, DestroyRef, effect, inject, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { map } from 'rxjs';
import {
  ArticleCardComponent,
  ArticleCardType,
  AuthService,
  BorsSimpleButtonComponent,
  createBorsOnlineTitle,
  defaultMetaInfo,
  SecureApiService,
} from 'src/app/shared';
import { CommentsComponent } from '../comments/comments.component';
import { BackendCommentWithArticle, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { profileComments } from '../../../shared/utils/profile.utils';
import { IMetaData, SeoService } from '@trendency/kesma-core';

@Component({
  selector: 'app-profile-comments',
  imports: [ArticleCardComponent, BorsSimpleButtonComponent, CommentsComponent],
  templateUrl: './profile-comments.component.html',
  styleUrl: './profile-comments.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProfileCommentsComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly destroyRef = inject(DestroyRef);
  private readonly secureApiService = inject(SecureApiService);
  private readonly authService = inject(AuthService);
  private readonly seoService = inject(SeoService);

  user = this.authService.currentUser;
  routeData = toSignal(this.route.data.pipe(map(({ data }) => data)));
  articlesWithComments = signal<BackendCommentWithArticle[]>([]);
  currentPage = signal(0);

  readonly ArticleCardType = ArticleCardType;

  constructor() {
    effect(() => {
      const data = this.routeData();
      if (!data) return;
      this.articlesWithComments.set(data.data);
    });
  }

  ngOnInit(): void {
    this.setMetaData();
  }

  loadMore(): void {
    const newPage = this.currentPage() + 1;
    this.currentPage.set(newPage);

    if (this.user()?.uid) {
      this.secureApiService
        .getMyComments({ page_limit: newPage, rowCount_limit: 5 }, this.user()!.uid)
        .pipe(
          map(({ data, meta }) => ({
            data: data.map((articles) => profileComments(articles)),
            meta,
          })),
          takeUntilDestroyed(this.destroyRef)
        )
        .subscribe({
          next: (newArticles) => {
            this.articlesWithComments.update((current) => [...current, ...newArticles.data]);
          },
        });
    }
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('profil/hozzaszolasok');
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle('Hozzászólásaim');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    };
    this.seoService.setMetaData(metaData);
  }
}

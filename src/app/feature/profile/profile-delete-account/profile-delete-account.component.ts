import { HttpErrorResponse } from '@angular/common/http';
import { ChangeDetectionStrategy, Component, inject, OnInit, signal } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { BackendFormErrors, createCanonicalUrlForPageablePage, markControlsTouched } from '@trendency/kesma-ui';
import { catchError, switchMap, throwError } from 'rxjs';
import {
  AppPasswordControlComponent,
  AuthService,
  BorsSimpleButtonComponent,
  createBorsOnlineTitle,
  defaultMetaInfo,
  PopupComponent,
  SecureApiService,
} from 'src/app/shared';
import { IMetaData, SeoService } from '@trendency/kesma-core';

@Component({
  selector: 'app-profile-delete-account',
  imports: [FormsModule, ReactiveFormsModule, AppPasswordControlComponent, BorsSimpleButtonComponent, PopupComponent],
  templateUrl: './profile-delete-account.component.html',
  styleUrl: './profile-delete-account.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProfileDeleteAccountComponent implements OnInit {
  private readonly authService = inject(AuthService);
  private readonly formBuilder = inject(UntypedFormBuilder);
  private readonly secureApiService = inject(SecureApiService);
  private readonly router = inject(Router);
  private readonly seoService = inject(SeoService);

  readonly formGroup = signal<UntypedFormGroup>(this.initForm);
  isLoading = signal<boolean>(false);
  error = signal<string | null>(null);
  isShowDeleteProfileConfirm = signal<boolean>(false);

  private get initForm(): UntypedFormGroup {
    return this.formBuilder.group({
      oldPassword: [null, [Validators.required]],
    });
  }

  ngOnInit(): void {
    this.setMetaData();
  }

  deleteAccount(): void {
    if (this.formGroup()) {
      markControlsTouched(this.formGroup());
    }

    if (!this.formGroup().valid) {
      return;
    }

    this.isShowDeleteProfileConfirm.set(false);
    this.secureApiService
      .deleteAccount(this.formGroup().value.oldPassword)
      .pipe(
        catchError((response: HttpErrorResponse) => {
          const backendErrors = response.error as BackendFormErrors;
          let isErrorHandled = false;
          if (backendErrors?.form?.errors?.children) {
            for (const [errorKey, value] of Object.entries(backendErrors.form.errors.children)) {
              // User old password is not correct
              if (errorKey === 'password' && !!value.errors) {
                this.formGroup().get('oldPassword')?.setErrors({ invalidOldPassword: true });
                isErrorHandled = true;
              }
            }
          }
          if (!isErrorHandled) {
            this.error.set('Ismeretlen hiba!');
          }
          this.isLoading.set(false);
          return throwError(() => 'Error');
        }),
        switchMap(() => this.authService.invalidate())
      )
      .subscribe(() => {
        this.router.navigate(['/']);
      });
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('profil/torles');
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle('Profil törlése');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    };
    this.seoService.setMetaData(metaData);
  }
}

@use 'shared' as *;

:host {
  width: 100%;
}

.profile {
  max-width: 864px;
  margin-bottom: 60px;

  &-title {
    font-size: 36px;
    font-weight: 800;
    line-height: 42px;
    margin-bottom: 32px;

    @include media-breakpoint-down(md) {
      font-size: 26px;
      line-height: 28px;
      text-align: center;
    }
  }
}

.articles {
  app-article-card {
    padding-top: 32px;
    border-top: 1px solid var(--kui-gray-300);
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    margin-bottom: 32px;

    @include media-breakpoint-down(md) {
      padding-top: 16px;
    }
  }
}

import { ChangeDetectionStrategy, Component, computed, DestroyRef, effect, inject, OnInit, signal, Signal } from '@angular/core';
import { ArticleCardComponent, ArticleCardType, BorsSimpleButtonComponent, createBorsOnlineTitle, defaultMetaInfo, SecureApiService } from '../../../shared';
import { ActivatedRoute } from '@angular/router';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { map } from 'rxjs';
import { ApiResponseMetaList, ApiResult, ArticleCard, createCanonicalUrlForPageablePage, LimitableMeta } from '@trendency/kesma-ui';
import { IMetaData, SeoService } from '@trendency/kesma-core';

@Component({
  selector: 'app-profile-saved-articles',
  imports: [ArticleCardComponent, BorsSimpleButtonComponent],
  templateUrl: './profile-saved-articles.component.html',
  styleUrl: './profile-saved-articles.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProfileSavedArticlesComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly secureApiService = inject(SecureApiService);

  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data as ApiResult<ArticleCard[], ApiResponseMetaList>)));
  readonly limitable: Signal<LimitableMeta | undefined> = computed(() => this.resolverData()?.meta.limitable);

  articles = signal<ArticleCard[]>([]);
  currentPage = signal<number>(0);

  readonly ArticleCardType = ArticleCardType;

  constructor() {
    effect(() => {
      const data = this.resolverData()?.data;
      if (data) {
        this.articles.set(data);
      }
    });
  }

  ngOnInit(): void {
    this.setMetaData();
  }

  loadMore(): void {
    const newPage = this.currentPage() + 1;
    this.currentPage.set(newPage);

    this.secureApiService
      .getSavedArticlesList(newPage, 5)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (newArticles) => {
          this.articles.update((current: ArticleCard[]) => [...current, ...newArticles.data]);
        },
      });
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('profil/mentett-cikkek', this.route.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle('Mentett cikkek');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    };
    this.seoService.setMetaData(metaData);
  }
}

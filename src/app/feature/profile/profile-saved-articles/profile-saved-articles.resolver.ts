import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { Observable } from 'rxjs';
import { ApiResponseMetaList, ApiResult, ArticleCard } from '@trendency/kesma-ui';
import { SecureApiService } from 'src/app/shared';

export const profileSavedArticlesResolver: ResolveFn<Observable<ApiResult<ArticleCard[], ApiResponseMetaList>>> = () => {
  const secureApiService = inject(SecureApiService);

  return secureApiService.getSavedArticlesList(0, 5);
};

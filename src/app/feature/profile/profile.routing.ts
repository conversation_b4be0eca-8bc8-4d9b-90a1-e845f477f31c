import { Routes } from '@angular/router';
import { ProfileComponent } from './profile.component';
import { PageValidatorGuard } from '@trendency/kesma-ui';
import { ProfileSettingsComponent } from './profile-settings/profile-settings.component';
import { AuthGuard } from 'src/app/shared';
import { ProfileDeleteAccountComponent } from './profile-delete-account/profile-delete-account.component';
import { profileSavedArticlesResolver } from './profile-saved-articles/profile-saved-articles.resolver';
import { ProfileSavedArticlesComponent } from './profile-saved-articles/profile-saved-articles.component';
import { ProfileCommentsComponent } from './profile-comments/profile-comments.component';
import { profileCommentsResolver } from './profile-comments/profile-comments.resolver';

export const PROFILE_ROUTES: Routes = [
  {
    path: '',
    component: ProfileComponent,
    canActivate: [PageValidatorGuard, AuthGuard], //AuthGuard
    children: [
      {
        path: 'beallitasok',
        component: ProfileSettingsComponent,
        canActivate: [PageValidatorGuard],
      },
      {
        path: 'hozzaszolasok',
        component: ProfileCommentsComponent,
        canActivate: [PageValidatorGuard],
        resolve: {
          data: profileCommentsResolver,
        },
        runGuardsAndResolvers: 'always',
      },
      {
        path: 'mentett-cikkek',
        component: ProfileSavedArticlesComponent,
        canActivate: [PageValidatorGuard],
        resolve: {
          data: profileSavedArticlesResolver,
        },
        runGuardsAndResolvers: 'always',
      },
      {
        path: 'torles',
        component: ProfileDeleteAccountComponent,
      },
      {
        path: '',
        redirectTo: 'beallitasok',
        pathMatch: 'full',
      },
    ],
  },
];

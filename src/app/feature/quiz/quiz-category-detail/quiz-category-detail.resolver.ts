import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';
import { QuizService } from '../quiz.service';
import { inject } from '@angular/core';
import { Quiz } from '../quiz.definitions';
import { ApiResult } from '@trendency/kesma-ui';
import { catchError, tap } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { throwError } from 'rxjs';

export const quizCategoryDetailResolver: ResolveFn<ApiResult<Quiz[]>> = (route: ActivatedRouteSnapshot) => {
  const quizService = inject(QuizService);
  const router = inject(Router);

  const { page } = route.queryParams;
  const { categorySlug } = route.params;

  const currentPage = page ? parseInt(page, 10) - 1 : 0;

  return quizService
    .getQuizListByCategorySlug(
      {
        page_limit: String(currentPage),
        rowCount_limit: '48',
      },
      categorySlug
    )
    .pipe(
      tap(({ data: quizList }) => {
        if (!quizList?.length) {
          throw Error(`Nincs kvíz ehhez a kategóriához: ${categorySlug}`);
        }
      }),
      catchError((error: HttpErrorResponse) => {
        router.navigate(['/404'], { skipLocationChange: true }).then();
        return throwError(() => error);
      })
    );
};

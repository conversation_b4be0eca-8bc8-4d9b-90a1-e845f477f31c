<section>
  <div class="wrapper">
    <app-breadcrumb [data]="breadcrumbItems"></app-breadcrumb>
    <app-block-title-row [data]="{ text: 'Kvíz' }"></app-block-title-row>
    <hr class="list-separator top" />
  </div>
  <div class="wrapper with-aside">
    <div class="left-column">
      <p class="introduction">
        Teszteld a tudásod – Játssz velünk minden nap! Unod már a sablonos netezést? Itt az ideje, hogy próbára tedd magad! Kvízgyűjtő oldalunkon naponta
        frissülő, szórakoztató és gondolkodtató kérdéssorok várnak – legyen szó sztárokról, filmekről, hétköznapi hősökről vagy logikai fejtörőkről. Akár
        villámkvízeket keresel a kávészünetre, ak<PERSON><PERSON> kih<PERSON>t jele<PERSON> tudáspróbákat, nálunk megtalálod!
      </p>
      @for (category of quizCategories(); track category.id; let index = $index) {
        <hr [class.first]="index === 0" class="list-separator" />

        <div class="category" [class.featured]="isFeaturedCategory(category)">
          <a class="category-title-link" [routerLink]="['/rovat/aktualis/kviz', category.slug]">
            <h2 class="category-title">{{ category.title }}</h2>
          </a>
          @if (category.quizzes?.length) {
            <div class="category-quizzes">
              @for (quiz of category.quizzes; track quiz.quizId) {
                <app-quiz-mini-card [title]="quiz.quizTitle" [link]="['/kvizek', quiz.quizSlug]"></app-quiz-mini-card>
              }
            </div>
          }
          <a [routerLink]="['/rovat/aktualis/kviz', category.slug]" class="category-link">
            Még {{ category.title }} kvíz
            <kesma-icon class="icon" name="arrow-right-long" [size]="16"></kesma-icon>
          </a>
          @if (!isFeaturedCategory(category)) {
            <hr class="list-separator" />
          }
        </div>
        @if (index === 0) {
          @if (adverts()?.desktop?.roadblock_1; as ad) {
            <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
          }
          @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
            <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
          }
        }
        @if (index === 3) {
          @if (adverts()?.desktop?.roadblock_2; as ad) {
            <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
          }
          @if (adverts()?.mobile?.mobilrectangle_2; as ad) {
            <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
          }
        }
      }
      @if (quizMeta()?.limitable; as limitable) {
        @if (limitable?.pageMax) {
          <app-pager
            [rowAllCount]="limitable?.rowAllCount"
            [rowOnPageCount]="limitable?.rowOnPageCount"
            [isListPager]="true"
            [hasSkipButton]="true"
            [allowAutoScrollToTop]="true"
            [maxDisplayedPages]="5"
          ></app-pager>
        }
      }
    </div>
    <aside>
      <app-sidebar />
    </aside>
  </div>
</section>

import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';
import { QuizService } from '../quiz.service';
import { inject } from '@angular/core';
import { QuizCategoryWithChildQuizzes } from '../quiz.definitions';
import { ApiResult, RedirectService } from '@trendency/kesma-ui';
import { catchError, tap } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { throwError } from 'rxjs';

export const quizCategoryListResolver: ResolveFn<ApiResult<QuizCategoryWithChildQuizzes[]>> = (route: ActivatedRouteSnapshot) => {
  const quizService = inject(QuizService);
  const redirectService = inject(RedirectService);
  const router = inject(Router);

  const { page } = route.queryParams;
  const currentPage = page ? parseInt(page, 10) - 1 : 0;

  return quizService.getQuizCategoryListWithChildQuizzes({ 'order_isFeatured[]': 'desc', page_limit: String(currentPage), rowCount_limit: 7 }).pipe(
    tap(({ data }) => {
      redirectService.shouldBeRedirect(currentPage, data) && redirectService.redirectOldUrl('kvizek', false, 302);
    }),
    catchError((error: HttpErrorResponse) => {
      router.navigate(['/404'], { skipLocationChange: true }).then();
      return throwError(() => error);
    })
  );
};

export type QuizCategory = Readonly<{
  id: string;
  title: string;
  slug: string;
}>;

export type Quiz = Readonly<{
  quizId: string;
  quizTitle: string;
  quizSlug: string;
  quizCategoryPageUrl?: string;
  quizCategoryId: string;
  quizCategoryTitle: string;
  quizCategorySlug: string;
  quizCategoryIsFeatured?: boolean;
  createdAt?: Date;
}>;

export type QuizCategoryWithChildQuizzes = QuizCategory &
  Readonly<{
    quizzes?: Quiz[];
  }>;

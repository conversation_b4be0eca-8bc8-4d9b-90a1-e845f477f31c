import { Routes } from '@angular/router';
import { QuizCategoryListComponent } from './quiz-category-list/quiz-category-list.component';
import { PageValidatorGuard } from '@trendency/kesma-ui';
import { quizCategoryListResolver } from './quiz-category-list/quiz-category-list.resolver';
import { QuizCategoryDetailComponent } from './quiz-category-detail/quiz-category-detail.component';
import { quizCategoryDetailResolver } from './quiz-category-detail/quiz-category-detail.resolver';

export const quizRouting: Routes = [
  {
    path: '',
    component: QuizCategoryListComponent,
    canActivate: [PageValidatorGuard],
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    pathMatch: 'full',
    resolve: {
      data: quizCategoryListResolver,
    },
  },
  {
    path: ':categorySlug',
    component: QuizCategoryDetailComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    resolve: {
      data: quizCategoryDetailResolver,
    },
  },
];

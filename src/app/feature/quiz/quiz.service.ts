import { inject, Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { forkJoin, Observable } from 'rxjs';
import { ApiResult, Quiz as KesmaQuiz } from '@trendency/kesma-ui';
import { Quiz, QuizCategory, QuizCategoryWithChildQuizzes } from './quiz.definitions';
import { map, switchMap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class QuizService {
  private readonly reqService = inject(ReqService);

  getQuizCategoryList(params: object): Observable<ApiResult<QuizCategory[]>> {
    return this.reqService.get<ApiResult<QuizCategory[]>>('content-group/source/quiz/quiz-category/list', {
      params,
    });
  }

  getQuizCategoryListWithChildQuizzes(params: object): Observable<ApiResult<QuizCategoryWithChildQuizzes[]>> {
    return this.reqService
      .get<ApiResult<QuizCategory[]>>('content-group/source/quiz/quiz-category/list', {
        params,
      })
      .pipe(
        switchMap((res) => {
          const categories = res.data;
          const categorySlugs = categories.map((category) => category.slug);
          const quizes$ = forkJoin(categorySlugs.map((categorySlug) => this.getQuizListByCategorySlug({ rowCount_limit: '4' }, categorySlug)));
          return quizes$.pipe(
            map((quizes) => {
              const cats = categories.map((category, index) => {
                return {
                  ...category,
                  quizzes: quizes[index]?.data,
                } as QuizCategoryWithChildQuizzes;
              });
              return {
                ...res,
                data: cats,
              };
            })
          );
        })
      );
  }
  /**
   * Creates string form a given query params object. This is used to create a query string that already contains
   * the params in the url. This is need for the requestService, as it makes a key only from the provided url, ignoring
   * the other params.
   * @param params
   * @private
   */
  private paramsToString(params: Record<string, string | string[] | undefined | null>): string {
    const paramsPrepared: Record<string, string>[] = [];
    if (params) {
      Object.keys(params).map((key) => {
        const value = params[key];
        if (value === undefined || value === null) {
          return;
        }
        if (Array.isArray(value)) {
          value.map((item) => {
            // Add [] to end of array params, but only if it is not already there.
            const itemKey = key.endsWith(']') ? key : `${key}[]`;
            paramsPrepared.push({ [itemKey]: encodeURIComponent(item) });
          });
          return;
        }
        paramsPrepared.push({ [key]: encodeURIComponent(value) });
      });
    }
    const paramsString = paramsPrepared
      .map((item) =>
        Object.keys(item)
          .map((key) => `${key}=${item[key]}`)
          .join('&')
      )
      .join('&');
    return paramsString;
  }
  /**
   * Creates a query string from the given query params and extra params. This is used to create a query string that
   * already contains the params in the url. This is need for the requestService, as it makes a key only from the provided url, ignoring
   * the other params.
   * @param queryParams
   * @param extraParams
   * @private
   */
  private searchKeywordParamsToString(
    queryParams: Record<string, string | string[] | undefined | null>,
    extraParams?: Record<string, string | string[]>
  ): string {
    const queryParamsString = queryParams ? this.paramsToString(queryParams) : '';
    const extraParamsString = extraParams ? this.paramsToString(extraParams) : '';

    return queryParamsString?.length && extraParamsString?.length ? `${queryParamsString}&${extraParamsString}` : queryParamsString || extraParamsString;
  }

  getQuizListByCategorySlug(params: Record<string, any>, categorySlug: string): Observable<ApiResult<Quiz[]>> {
    const paramsAsString = this.searchKeywordParamsToString(params, {
      quizCategorySlug_filter: categorySlug,
      'order_createdAt[]': 'desc',
    });
    return this.reqService.get<ApiResult<Quiz[]>>(`content-group/quiz/quiz-category/list${paramsAsString ? `?${paramsAsString}` : ''}`);
  }

  getQuizBySlug(quizSlug: string): Observable<ApiResult<KesmaQuiz>> {
    return this.reqService.get<ApiResult<KesmaQuiz>>(`content-group/quiz/${quizSlug}`, {});
  }
}

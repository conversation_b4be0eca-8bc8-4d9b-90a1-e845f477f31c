<section class="register-form">
  <div class="wrapper register-form-wrapper">
    @if (!isSubmitted()) {
      <div class="register-form-header">
        <h1 class="register-form-header-title">Fiók létrehozása</h1>
        <p class="register-form-header-text">
          <PERSON>zzon létre egy új Bors-felhasz<PERSON>lói fiókot, és használja ki a regisztrált olvasóknak járó előnyöket! Ha m<PERSON><PERSON>,
          <a [routerLink]="['/', 'bejelentkezes']" class="register-form-header-text-link"> itt tud bejelentkezni. </a>
        </p>
      </div>
      @if (formGroup() && allowedLoginMethods()) {
        <form (ngSubmit)="register()" [formGroup]="formGroup()">
          @if (allowedLoginMethods()?.email) {
            <app-input-control controlName="username" labelText="Felhasználónév" placeholder="Adja meg a felhasználónevét" [isRequired]="true">
              <small>
                Ez a becenév fog megjelenni a hozzászólásoknál, mely maximum 100 karakter hosszú lehet. Legalább 6 karakterből kell állnia, és a következőket
                tartalmazhatja: kis- és nagybetű, szám, kötőjel, alsóvonás.
              </small>
            </app-input-control>
            <app-input-control controlName="email" labelText="E-mail cím" placeholder="Adja meg az e-mail címét" [isRequired]="true">
              <small>Ezzel a címmel fogja tudni elérni Bors-felhasználói fiókját</small>
            </app-input-control>
            <app-password-control [isRequired]="true">
              <small> A választott jelszónak legalább 6 karakterből kell állnia, és tartalmaznia kell kisbetűt, nagybetűt és számot. </small>
            </app-password-control>
            <div class="register-form-privacy">
              <kesma-form-control class="checkbox">
                <label class="bors-form-checkbox" for="newsletter">
                  <input type="checkbox" id="newsletter" formControlName="newsletter" />
                  <span>Feliratkozom a BORS hírlevélre</span>
                </label>
              </kesma-form-control>
              <kesma-form-control class="checkbox">
                <label class="bors-form-checkbox" for="terms">
                  <input type="checkbox" id="terms" formControlName="terms" />
                  <span
                    >Elolvastam és megértettem a BorsOnline.hu
                    <a [routerLink]="['/felhasznalasi-feltetelek']" class="bors-form-checkbox-label-link" target="_blank">felhasználási feltételeit</a>
                    és a Mediaworks Hungary Zrt.
                    <a [routerLink]="['/adatvedelem']" class="bors-form-checkbox-label-link" target="_blank">adatvédelmi tájékoztatóját</a>, és hozzájárulok,
                    hogy a megadott adataimat a szolgáltató mint adatkezelő a szabályzatban foglaltaknak megfelelően kezelje. *</span
                  >
                </label>
              </kesma-form-control>
              <kesma-form-control class="checkbox">
                <label class="bors-form-checkbox" for="marketing">
                  <input type="checkbox" id="marketing" formControlName="marketing" />
                  <span
                    >Hozzájárulok ahhoz, hogy a megadott személyes adataimat a Mediaworks Hungary Zrt. - hozzájárulásom visszavonásáig - közvetlen üzletszerzési
                    célra (tájékoztatás, közvélemény- vagy piackutatás, illetve egyéb tájékoztatás – sorsolás, tájékoztatók, kereskedelmi és marketing ajánlatok
                    eljuttatására) felhasználja, és ezzel kapcsolatosan engem az általam megadott elérhetőségeken (így elektronikus levelezés útján vagy postai
                    úton vagy telefonon) megkeressen, illetve részemre nyomtatott és/vagy online sajtótermékekkel kapcsolatos gazdasági reklámot küldjön. A
                    hozzájárulás visszavonására az
                    <a [routerLink]="['/adatvedelem']" class="bors-form-checkbox-label-link" target="_blank">ADATVÉDELMI TÁJÉKOZTATÓBAN</a>
                    foglaltak szerint van lehetőségem.</span
                  >
                </label>
              </kesma-form-control>
            </div>
            @if (error()) {
              <div class="general-form-error">{{ error() }}</div>
            }
            <app-simple-button class="register-form-button w-100" [disabled]="isLoading()" [isSubmit]="true"
              >{{ isLoading() ? 'Kérem, várjon...' : 'Regisztráció' }}
            </app-simple-button>
          }
        </form>
      }
    } @else {
      <div class="register-form-header">
        <h1 class="register-form-header-title">E-mail ellenőrzése</h1>
        <p class="register-form-header-text">Az aktiváláshoz szükséges adatokat e-mailben küldtük el. <br />Kérjük ellenőrizze az e-mail fiókját!</p>
      </div>
      <app-simple-button [routerLink]="'/'">Tovább a címlapra</app-simple-button>
    }
  </div>
</section>

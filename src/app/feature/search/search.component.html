<section>
  <div class="wrapper">
    <app-search-filter></app-search-filter>
    <app-breadcrumb
      [data]="[
        {
          label: 'Keresés',
        },
      ]"
    ></app-breadcrumb>
    <div class="search-results-description">
      @if (resultsCountTo) {
        <p class="search-results-description-text">
          {{ resultsCountFrom }}-{{ resultsCountTo }} találat megjelenítése a(z) {{ limitable()?.rowAllCount }}-ből a következő témában:
        </p>
      } @else {
        <p class="search-results-description-text">Nincs találat a követke<PERSON>ő témában:</p>
      }

      @if (globalFilter()) {
        <h1 class="search-results-description-keyword">{{ globalFilter() }}</h1>
      }
    </div>
    @if (articles()?.length > 0) {
      <div class="search-main-article">
        @if (articles()?.[0]; as mainArticle) {
          <app-article-card [styleId]="ArticleCardType.HighlightedSideImgDateTitleLead" [data]="mainArticle" [useEagerLoad]="true"></app-article-card>
        }
      </div>
    }
  </div>

  <div class="wrapper with-aside">
    <div class="left-column">
      <div class="search-results-list">
        @if (articles()?.length >= 2) {
          @for (article of articles() | slice: 1; track article.id; let i = $index) {
            @if (article) {
              <app-article-card [styleId]="ArticleCardType.SideImgDateTitleLead" [data]="article"></app-article-card>
            }
            @if (i === 0) {
              @if (adverts()?.desktop?.roadblock_1; as ad) {
                <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
              }
              @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
                <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
              }
            }
            @if (i === 2) {
              @if (adverts()?.desktop?.roadblock_2; as ad) {
                <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
              }
              @if (adverts()?.mobile?.mobilrectangle_2; as ad) {
                <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
              }
            }
          }
        }
      </div>
      @if (resultsCountTo > 0) {
        <app-pager class="search-pager" [rowAllCount]="limitable()?.rowAllCount" [rowOnPageCount]="limitable()?.rowOnPageCount!"></app-pager>
      }
    </div>
    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>

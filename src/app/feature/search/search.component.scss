@use 'shared' as *;

:host {
  color: var(--kui-black-950);
  display: block;
  margin: 48px 0;

  .search {
    &-results-description {
      padding-bottom: 32px;
      border-bottom: 1px solid var(--kui-gray-300);
      margin-bottom: 32px;

      &-text {
        font-size: 16px;
        font-weight: 400;
        line-height: 42px;

        @include media-breakpoint-down(md) {
          line-height: 20px;
          padding-top: 16px;
          padding-bottom: 8px;
        }
      }

      &-keyword {
        width: 100%;
        color: var(--kui-red-500);
        font-size: 36px;
        font-weight: 800;
        line-height: 42px;

        @include media-breakpoint-down(md) {
          font-size: 26px;
          line-height: 28px;
        }
      }
    }

    &-main-article {
      padding-bottom: 32px;
      border-bottom: 1px solid var(--kui-gray-300);
      margin-bottom: 32px;

      @include media-breakpoint-down(md) {
        padding-bottom: 16px;
        margin-bottom: 16px;
      }
    }

    &-pager {
      margin: 64px 0;

      @include media-breakpoint-down(md) {
        margin: 32px 0;
      }
    }
  }
}

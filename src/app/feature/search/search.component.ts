import { SlicePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, effect, inject, Signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  ArticleCard,
  createCanonicalUrlForPageablePage,
  LimitableMeta,
} from '@trendency/kesma-ui';
import { toNumber } from 'lodash-es';
import { map } from 'rxjs/operators';
import {
  ArticleCardComponent,
  ArticleCardType,
  BreadcrumbComponent,
  createBorsOnlineTitle,
  defaultMetaInfo,
  getNewestArticleThumbnail,
  PagerComponent,
  SearchFilterComponent,
} from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { SearchData } from './search.definitions';

const MAX_RESULTS_PER_PAGE = 10;

@Component({
  selector: 'app-search',
  imports: [PagerComponent, SearchFilterComponent, ArticleCardComponent, SlicePipe, BreadcrumbComponent, SidebarComponent, AdvertisementAdoceanComponent],
  templateUrl: './search.component.html',
  styleUrl: './search.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SearchComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly seo = inject(SeoService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  readonly ArticleCardType = ArticleCardType;

  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data as SearchData)));

  readonly articles: Signal<ArticleCard[] | undefined> = computed(() => this.resolverData()?.articles);
  readonly limitable: Signal<LimitableMeta | undefined> = computed(() => this.resolverData()?.limitable);
  readonly globalFilter = computed(() => {
    const globalFilter = this.resolverData()?.globalFilter;
    this.setMetaData(globalFilter);
    return globalFilter;
  });
  readonly page = toSignal(this.route.queryParamMap.pipe(map((params) => toNumber(params.get('page')) ?? 0)));
  resultsCountFrom: number = 0;
  resultsCountTo: number = 0;

  readonly adverts = toSignal(this.adStoreAdo.advertisemenets$.pipe(map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads))));

  constructor() {
    effect(() => {
      const limitable = this.limitable();
      if (limitable) {
        this.calculateResultsCount();
      }
    });
  }

  private calculateResultsCount(): void {
    const pageBase = this.page() ?? 0;
    const page = pageBase === 0 ? 1 : pageBase;
    const pagePrevious = page - 1;
    const pageMaxResults = page * MAX_RESULTS_PER_PAGE;
    const rowAllCount = this.limitable()?.rowAllCount;
    const rowOnPageCount = this.limitable()?.rowOnPageCount ?? 0;
    if (rowAllCount && rowAllCount > 0) {
      this.resultsCountTo = pageMaxResults < rowAllCount ? rowOnPageCount * page : rowAllCount;
      this.resultsCountFrom = rowOnPageCount * pagePrevious + 1;
    } else {
      this.resultsCountTo = rowAllCount ?? 0;
      this.resultsCountFrom = rowAllCount ?? 0;
    }
  }

  private setMetaData(globalFilter?: string): void {
    const plainTitle = globalFilter ? `Keresés: ${globalFilter}` : 'Keresés';
    const title = createBorsOnlineTitle(plainTitle);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      ogImage: getNewestArticleThumbnail(this.articles() ?? [], this.seo.hostUrl),
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('kereses');
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }
}

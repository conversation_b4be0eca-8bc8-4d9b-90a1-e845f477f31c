<section>
  <div class="wrapper">
    <app-breadcrumb [data]="[{ label: 'Shorts' }]" />
    <h1 class="title">Shorts</h1>
  </div>
</section>

<div class="borsShorts-list" #shortsContainer>
  @for (shortVideo of shortVideos(); track shortVideo.id; let i = $index) {
    <div class="short" id="short-{{ $index }}">
      <iframe
        [title]="shortVideo.title"
        #iframe
        class="iframe"
        [attr.data-index]="$index"
        [src]="
          ($index === currentIndex() ? shortVideo.videaUrl + (shortVideo.videaUrl.includes('?') ? '&' : '?') + 'autoplay=1&mute=1' : shortVideo.videaUrl)
            | bypass: 'resourceUrl'
        "
        allowfullscreen="allowfullscreen"
        allow="autoplay; fullscreen"
      ></iframe>
    </div>

    @if (i === 1) {
      @if (adverts()?.desktop?.roadblock_1; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
      }
      @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
      }
    }
    @if (i === 5) {
      @if (adverts()?.desktop?.roadblock_2; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
      }
      @if (adverts()?.mobile?.mobilrectangle_2; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
      }
    }
  }
  @if (pageMax > currentPage()) {
    <app-simple-button class="short last" [wide]="true" (keydown)="loadMore()" (click)="loadMore()">Még több...</app-simple-button>
  }
</div>

<div class="controls" [style.right]="'calc(50% - ' + shortsContainer.clientWidth + 'px / 2 - 50px)'">
  <kesma-icon class="pagination up" name="chevron-right-new" (keydown)="scrollUp()" (click)="scrollUp()" [class.disabled]="isAtStart()" />
  <kesma-icon class="pagination down" name="chevron-right-new" (keydown)="swipeDown()" (click)="swipeDown()" [class.disabled]="isAtEnd()" />
</div>

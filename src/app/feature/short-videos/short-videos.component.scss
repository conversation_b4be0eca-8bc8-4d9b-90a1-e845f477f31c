@use 'shared' as *;

:host {
  display: block;
  margin-block: 32px;
  margin-bottom: -96px;

  @include media-breakpoint-down(md) {
    margin-bottom: -64px;
  }

  .wrapper {
    overflow: hidden;
  }

  .title {
    font-size: 36px;
    font-weight: 800;
    line-height: 42px;
    color: var(--kui-red-500);

    @include media-breakpoint-down(md) {
      font-size: 26px;
      font-weight: 800;
      line-height: 28px;
    }
  }

  .borsShorts-list {
    width: fit-content;
    margin-inline: auto;
  }
  .short {
    scroll-snap-align: start;
    height: calc(100vh - (#{var(--scroll-snap-padding)} + 60px));
    margin-block: 10px;
    @include media-breakpoint-down(xs) {
      margin-inline: 15px;
    }

    &.last {
      height: 20px;
    }
  }
  .controls {
    position: fixed;
    right: 20px;
    bottom: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    @include media-breakpoint-down(sm) {
      display: none;
    }
  }
  .pagination {
    background: var(--kui-red-400);
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    color: var(--kui-white);
    width: 36px;
    height: 36px;
  }
  .up {
    rotate: -90deg;
  }
  .down {
    rotate: 90deg;
  }
  .iframe {
    width: 100%;
    height: 100%;
    aspect-ratio: 9/16;
    object-fit: contain;
    border-radius: 4px;
    border: none;
  }
  .disabled {
    pointer-events: none;
    background-color: var(--kui-gray-400);
    color: var(--kui-black);
  }
}

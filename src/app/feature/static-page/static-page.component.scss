@use 'shared' as *;

:host {
  ::ng-deep kesma-layout {
    margin-top: 0;
  }
  .wrapper.with-aside {
    margin-top: 0;
  }
  .page-header {
    padding-top: 32px;
    display: flex;
    flex-direction: column;
    @include media-breakpoint-down(md) {
      gap: 8px;
    }
  }
  .heading-line {
    .title {
      color: var(--kui-red-500);
      font-size: 36px;
      font-weight: 800;
      line-height: 42px;
      margin-top: 30px;
    }
  }
  .divider {
    background-color: var(--kui-black);
    opacity: 0.2;
    margin-block: 32px;
    height: 1px;
    @include media-breakpoint-down(md) {
      margin-block: 16px;
    }
  }
}

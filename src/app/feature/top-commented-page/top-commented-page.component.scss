@use 'shared' as *;

:host {
  display: block;
  margin: 32px 0;
  .header {
    display: flex;
    flex-direction: column;
    @include media-breakpoint-down(md) {
      gap: 8px;
    }
  }
  .divider {
    height: 1px;
    background-color: rgba($black, 0.2);
    margin-block: 32px;
    width: 100%;
    @include media-breakpoint-down(md) {
      margin-block: 16px;
    }
  }
  .content {
    display: flex;
    align-items: flex-start;
    gap: 32px;
    @include media-breakpoint-down(md) {
      gap: 16px;
    }
    &-divider {
      height: 1px;
      margin-block: 16px;
      background-color: var(--kui-gray-200);
      width: 100%;
    }
  }
  .rank {
    width: 40px;
    height: 40px;
    border: 2px solid var(--kui-red-400);
    color: var(--kui-red-400);
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
    text-align: center;
    flex-shrink: 0;
  }
}

<section>
  <div class="wrapper">
    <app-breadcrumb [data]="[{ label: 'Videók', url: '/video' }]" />
    @if (resolverData()) {
      <kesma-article-video [data]="videoData()"></kesma-article-video>
      <div class="data-wrapper">
        <div>
          <h1 class="video-title">{{ resolverData()?.title }}</h1>
          <a class="video-column" [routerLink]="['/rovat', resolverData()?.columnSlug]">{{ resolverData()?.columnTitle }}</a>
        </div>
        <div class="video-dates">
          @if (resolverData()?.publishDate) {
            <span><strong>PUBLIKÁLÁS: &nbsp;</strong> {{ resolverData()?.publishDate | dfnsFormat: 'yyyy. LLLL dd. HH:mm' }}</span>
          }
          @if (resolverData()?.length) {
            <div class="video-dates-right">
              <kesma-icon class="video-icon" name="video"></kesma-icon><span class="video-length">{{ resolverData()?.length | intervalToDuration }}</span>
            </div>
          }
        </div>
        <div class="video-tags-block">
          @for (tag of resolverData()?.tags; track $index) {
            <a class="video-tag" [routerLink]="['/cimke', tag.slug]">{{ tag.title }}</a>
          }
        </div>
        <div class="video-lead">
          {{ resolverData()?.lead }}
        </div>
        <app-social-share></app-social-share>
        <div class="video-description">{{ resolverData()?.description }}</div>
      </div>
    }
  </div>
</section>

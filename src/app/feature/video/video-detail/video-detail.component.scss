@use 'shared' as *;

:host {
  color: var(--kui-black-950);
}

.wrapper {
  display: flex;
  flex-direction: column;
  row-gap: 32px;
  margin-top: 32px;

  @include media-breakpoint-down(sm) {
    row-gap: 16px;
  }
}

.data-wrapper {
  margin: 0 auto;
  max-width: 864px;
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 32px;

  @include media-breakpoint-down(sm) {
    row-gap: 16px;
  }
}

.video {
  &-title {
    font-size: 52px;
    font-weight: 800;
    line-height: 60px;
    margin-bottom: 5px;

    @include media-breakpoint-down(sm) {
      font-size: 32px;
      line-height: 38px;
    }
  }

  &-column {
    font-size: 16px;
    font-weight: 700;
    text-transform: uppercase;
    color: var(--kui-red-500);

    @include media-breakpoint-down(sm) {
      font-size: 16px;
    }
  }

  &-dates {
    border-block: 1px solid var(--kui-gray-200);
    padding: 16px 0;
    display: flex;
    align-items: center;
    font-size: 12px;
    line-height: 16px;
    text-transform: uppercase;

    @include media-breakpoint-down(sm) {
      justify-content: space-between;
    }

    &-right {
      display: flex;
      flex-direction: row;
    }
  }

  &-icon {
    width: 16px;
    margin-left: 40px;
    margin-right: 8px;
  }

  &-length {
    font-size: 12px;
    font-weight: 700;
    line-height: 16px;
    text-transform: uppercase;
  }

  &-tags-block {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 4px;
  }

  &-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 600;
    font-variant: all-small-caps;
    color: var(--kui-red-500);
    border: 1px solid var(--kui-red-500);
  }

  &-lead {
    font-size: 24px;
    font-weight: 600;
    line-height: 30px;

    @include media-breakpoint-down(sm) {
      font-size: 20px;
      line-height: 28px;
    }
  }

  &-description {
    font-size: 18px;
    font-weight: 400;
    line-height: 26px;
    margin-bottom: 24px;

    @include media-breakpoint-down(sm) {
      font-size: 16px;
      line-height: 24px;
      margin-bottom: 32px;
    }
  }
}

app-social-share {
  padding: 16px 0;
  border-block: 1px solid var(--kui-gray-200);
}

<article class="video" [class.featured]="isFeatured()" [class.highlighted]="isFirst()">
  <a class="video-thumbnail" [routerLink]="videoUrl()">
    @let displayedUrl = isFirst() ? (video().coverImageUrl ?? video().coverImage?.thumbnailUrl) : (video().coverImage?.thumbnailUrl ?? video().coverImageUrl);
    <img
      withFocusPoint
      [data]="video().coverImage?.focusedImages ?? {}"
      [displayedAspectRatio]="{ desktop: '16:9' }"
      [displayedUrl]="displayedUrl ?? PlaceholderImg"
      alt=""
      [class.is-placeholder]="!displayedUrl"
      class="video-thumbnail-image"
      fetchpriority="high"
    />

    @if (isFeatured() && isFirst()) {
      <kesma-icon name="play" class="video-thumbnail-play" [size]="80" />
    }

    <span class="video-thumbnail-tag">
      <kesma-icon name="video" [size]="16" />
      <span class="video-thumbnail-tag-text">Videó</span>
    </span>
  </a>
  <div class="video-details">
    @if (isFeatured()) {
      <a [routerLink]="videoUrl()" class="video-details-time">
        <span class="video-details-time-published">
          {{ video().publicDate | dfnsFormat: 'yyyy.MM.dd' }}
        </span>
        @if (video().length; as length) {
          <span class="video-details-time-separator">|</span>
          <span class="video-details-time-length">{{ length | intervalToDuration }}</span>
        }
      </a>
    }
    <h2 class="video-details-title">
      <a [routerLink]="videoUrl()">{{ video().title }}</a>
    </h2>
    @if (video().column; as column) {
      <a class="video-details-column" [routerLink]="columnUrl()">{{ column.title }}</a>
    }
  </div>
</article>

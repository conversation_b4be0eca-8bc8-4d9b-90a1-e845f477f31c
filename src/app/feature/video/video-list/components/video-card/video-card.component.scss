@use 'shared' as *;

:host {
  display: block;
  width: 100%;
}

.video {
  display: flex;
  flex-direction: column;
  gap: 16px;

  &-thumbnail {
    display: block;
    position: relative;

    &-image {
      width: 100%;
      height: 100%;
      object-fit: cover;

      &.is-placeholder {
        object-fit: contain;
        background-color: var(--kui-red-500);
      }
    }

    &-play {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: var(--kui-white);
      opacity: 0.5;
    }

    &-tag {
      position: absolute;
      display: flex;
      align-items: center;
      bottom: 10px;
      left: 0;
      padding: 4px;
      background-color: var(--kui-black-950);
      color: var(--kui-white);
      font-size: 12px;
      font-weight: 600;

      &-text {
        text-transform: uppercase;
      }

      kesma-icon {
        margin-right: 4px;
        display: inline-flex;
        fill: var(--kui-white);
      }
    }
  }

  &-details {
    font-size: 14px;
    font-weight: 600;

    &-time {
      display: flex;
      gap: 8px;
      margin-bottom: 12px;

      &-separator {
        color: #99a7ae;
      }
    }

    &-title {
      font-size: 20px;
      font-weight: 700;
      line-height: 24px;
      margin-bottom: 12px;
    }

    &-column {
      color: var(--kui-red-500);
      font-size: 12px;
      font-weight: 500;
      line-height: 18px;
      text-transform: uppercase;
    }
  }

  @include media-breakpoint-up(md) {
    &.featured {
      &:not(.highlighted) {
        flex-direction: row;

        .video {
          &-thumbnail {
            min-width: fit-content;

            &-image {
              width: 220px;
              height: 124px;
            }
          }

          &-details {
            width: 100%;
          }
        }
      }

      &.highlighted {
        font-size: 16px;

        .video-details {
          &-title {
            font-size: 30px;
            font-weight: 800;
            line-height: 38px;
            margin-bottom: 16px;
          }
        }
      }
    }
  }
}

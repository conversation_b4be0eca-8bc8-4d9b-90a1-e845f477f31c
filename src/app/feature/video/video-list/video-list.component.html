<section>
  <div class="wrapper">
    <div class="header">
      <app-breadcrumb [data]="[{ label: 'Videók' }]" />
      <h1 class="header-title">Videók</h1>
    </div>

    <div class="featured-videos">
      @for (video of featuredVideos(); track video.id) {
        <app-video-card [video]="video" [isFirst]="$first" [isFeatured]="true" [class.highlighted]="$first" />
      }
    </div>

    @if (adverts()?.desktop?.roadblock_1; as ad) {
      <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
    }
    @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
      <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
    }

    <app-short-videos-box [shortVideos]="shorts()" />
    <hr class="divider" />
  </div>

  <div class="wrapper with-aside">
    <div class="left-column">
      <div class="video-list">
        @for (video of normalVideos(); track video.id) {
          <app-video-card [video]="video" />
        }
      </div>

      @if (adverts()?.desktop?.roadblock_2; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
      }
      @if (adverts()?.mobile?.mobilrectangle_2; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--ad-margin)' }" [ad]="ad"></kesma-advertisement-adocean>
      }

      <app-pager [rowAllCount]="limitable()?.rowAllCount ?? 0" [rowOnPageCount]="limitable()?.rowOnPageCount ?? 0" />
    </div>
    <aside>
      <app-sidebar />
    </aside>
  </div>
</section>

import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService } from '@trendency/kesma-ui';
import { FormatPipeModule } from 'ngx-date-fns';
import { map } from 'rxjs/operators';
import { ArticleCardType, BreadcrumbComponent, PagerComponent, ShortVideosBoxComponent } from '../../../shared';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { VideoListData } from './api/video-list.definitions';
import { VideoCardComponent } from './components/video-card/video-card.component';

@Component({
  selector: 'app-video-list',
  imports: [
    BreadcrumbComponent,
    FormatPipeModule,
    VideoCardComponent,
    ShortVideosBoxComponent,
    PagerComponent,
    SidebarComponent,
    AdvertisementAdoceanComponent,
  ],
  templateUrl: './video-list.component.html',
  styleUrl: './video-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VideoListComponent {
  private readonly routeData = toSignal(inject(ActivatedRoute).data.pipe(map(({ data }) => data as VideoListData)));
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);

  readonly adverts = toSignal(this.adStoreAdo.advertisemenets$.pipe(map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads))));

  readonly ArticleCardType = ArticleCardType;

  readonly featuredVideos = computed(() => (this.routeData()?.videos?.data ?? []).slice(0, 5));
  readonly normalVideos = computed(() => (this.routeData()?.videos?.data ?? []).slice(5));
  readonly limitable = computed(() => this.routeData()?.videos?.meta?.limitable ?? {});

  readonly shorts = computed(() => this.routeData()?.shorts ?? []);
}

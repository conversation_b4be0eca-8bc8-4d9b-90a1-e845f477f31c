@if (data) {
  <article class="article">
    @switch (styleId()) {
      @case (ArticleCardType.MainArticle) {
        <ng-container
          *ngTemplateOutlet="ArticleThumbnailTemplate; context: { isSlant: desktopWidth() !== 12, displayedAspectRatio: { desktop: '16:9' } }"
        ></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { showComments: true }"></ng-container>
      }
      @case (ArticleCardType.TopSlantImgTagTitle) {
        <ng-container *ngTemplateOutlet="ArticleThumbnailTemplate; context: { isSlant: true }"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { showComments: true }"></ng-container>
      }
      @case (ArticleCardType.TopSlantImgTagTitlePadding) {
        <ng-container *ngTemplateOutlet="ArticleThumbnailTemplate; context: { isSlant: true }"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { showComments: true }"></ng-container>
      }
      @case (ArticleCardType.NoImgTagTitle) {
        <ng-container *ngTemplateOutlet="ArticleSponsorshipTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { showComments: true, showBadges: true }"></ng-container>
      }
      @case (ArticleCardType.TopImgTagTitle) {
        <ng-container *ngTemplateOutlet="ArticleThumbnailTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { showComments: true }"></ng-container>
      }
      @case (ArticleCardType.SideImgDateTitleLead) {
        <ng-container *ngTemplateOutlet="ArticleThumbnailTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { showComments: true, showDate: true, showLead: true }"></ng-container>
      }
      @case (ArticleCardType.HighlightedSideImgDateTitleLead) {
        <ng-container *ngTemplateOutlet="ArticleThumbnailTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { showComments: true, showDate: true, showLead: true }"></ng-container>
      }
      @case (ArticleCardType.MostReadNews) {
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { showComments: true, showBadges: true, withoutImg: true }"></ng-container>
      }
      @case (ArticleCardType.MostRecentOrMostRead) {
        <ng-content></ng-content>
        <ng-container
          *ngTemplateOutlet="ArticleContentTemplate; context: { showComments: true, showBadges: true, withoutImg: true, boldWordLength: 3 }"
        ></ng-container>
      }
      @case (ArticleCardType.MinuteToMinute) {
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { withoutImg: true }"></ng-container>
      }
      @case (ArticleCardType.ArticleRecommendationTopImg) {
        <ng-container *ngTemplateOutlet="ArticleThumbnailTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { hideTag: true, withRecommendedTitle: true }"></ng-container>
      }
      @case (ArticleCardType.ArticleRecommendationSideImg) {
        <ng-container *ngTemplateOutlet="ArticleThumbnailTemplate; context: { hideBadges: true }"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { showComments: true, showBadges: true }"></ng-container>
      }
      @case (ArticleCardType.ArticleRecommendationNoImg) {
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { showBadges: true, withRecommendedTitle: true }"></ng-container>
      }
      @case (ArticleCardType.Weather) {
        <ng-container *ngTemplateOutlet="ArticleThumbnailTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { showComments: true }"></ng-container>
      }
      @case (ArticleCardType.ExternalRecommendation) {
        <a class="article-link" [href]="data?.url" target="_blank">
          <img
            class="thumbnail"
            withFocusPoint
            [data]="data?.thumbnailFocusedImages"
            [displayedUrl]="displayedThumbnailUrl()"
            [displayedAspectRatio]="{ desktop: '3:2' }"
            [alt]="data?.thumbnail?.alt ?? data?.title"
            loading="lazy"
          />
        </a>
        <div class="article-data">
          <a class="article-link" [href]="data?.url" target="_blank">
            <h2 class="title">{{ data?.title }}</h2>
          </a>
          @if (data?.category?.name) {
            <div class="domain">{{ data?.category?.name }}</div>
          }
        </div>
      }
    }
  </article>
}

<ng-template #ArticleThumbnailTemplate let-isSlant="isSlant" let-displayedAspectRatio="displayedAspectRatio" let-hideBadges="hideBadges">
  <a class="article-link thumbnail-wrapper relative" [routerLink]="articleLink()">
    @if (showAdultLayer()) {
      <kesma-adult-overlay>
        <ng-container *ngTemplateOutlet="Image"></ng-container>
      </kesma-adult-overlay>
    } @else {
      <ng-container *ngTemplateOutlet="Image"></ng-container>
    }
    <ng-template #Image>
      <img
        class="thumbnail"
        [class.slant-thumbnail]="isSlant"
        [class.is-placeholder]="displayedThumbnailUrl() === PlaceholderImg"
        withFocusPoint
        [data]="data?.thumbnailFocusedImages"
        [displayedUrl]="displayedThumbnailUrl()"
        [displayedAspectRatio]="displayedAspectRatio || { desktop: '3:2' }"
        [alt]="data?.thumbnail?.alt ?? data?.title"
        [attr.loading]="useEagerLoad() ? 'eager' : 'lazy'"
        [attr.fetchpriority]="fetchpriority()"
      />
    </ng-template>

    <ng-container *ngTemplateOutlet="ArticleSponsorshipTemplate"></ng-container>
    @if (!hideBadges) {
      <ng-container *ngTemplateOutlet="ArticleBadgesTemplate"></ng-container>
    }
  </a>
</ng-template>

<ng-template
  #ArticleContentTemplate
  let-showDate="showDate"
  let-showComments="showComments"
  let-showBadges="showBadges"
  let-showLead="showLead"
  let-hideTag="hideTag"
  let-withoutImg="withoutImg"
  let-boldWordLength="boldWordLength"
  let-withRecommendedTitle="withRecommendedTitle"
>
  <div class="article-data" [class.no-image]="withoutImg" [class.left-border]="hasLeftBorder()">
    @if (showDate && publishDate()) {
      <div class="article-date">{{ publishDate() | dfnsFormat: 'yyyy. LL. dd.' }}</div>
    }
    <a class="article-link" [routerLink]="articleLink()">
      <h2 class="title">
        @if (isOpinion()) {
          <span class="opinion-text">VÉLEMÉNY:</span> “{{ data?.title }}”
        } @else if (isExclusive()) {
          <span class="exclusive-text">EXKLUZÍV:</span> {{ data?.title }}
        } @else if (isBreaking()) {
          <span class="breaking-text">BREAKING:</span> {{ data?.title }}
        } @else {
          @if (boldWordLength && separateBoldTitle(boldWordLength, data?.title); as titles) {
            <b>{{ titles.bold }}</b> {{ titles.normal }}
          } @else {
            @if (withRecommendedTitle) {
              {{ data?.recommendedTitle ?? data?.title }}
            } @else {
              {{ data?.title }}
            }
          }
        }
      </h2>
    </a>
    @if (showLead && data?.lead) {
      <div class="lead">{{ data?.lead }}</div>
    }
    @if (showBadges) {
      <ng-container *ngTemplateOutlet="ArticleBadgesTemplate"></ng-container>
    }
    @if (!hideTag) {
      @if (showComments && data?.commentCount && !data?.isCommentsDisabled) {
        <div class="article-bottom">
          <ng-container *ngTemplateOutlet="ArticleDisplayedTagTemplate"></ng-container>
          <div class="comment-count">
            <kesma-icon name="comment" size="16"></kesma-icon>
            {{ data?.commentCount }}
          </div>
        </div>
      } @else {
        <ng-container *ngTemplateOutlet="ArticleDisplayedTagTemplate"></ng-container>
      }
    }
  </div>
</ng-template>

<ng-template #ArticleBadgesTemplate>
  @if (hasBadges) {
    <div class="badges">
      @if (toBool(data?.hasGallery)) {
        <div class="with-icon">
          <kesma-icon size="16" height="12.5" name="gallery" />
          Galéria
        </div>
      }
      @if (toBool(data?.isVideoType)) {
        <div class="with-icon">
          <kesma-icon size="16" name="video" />
          Videó
        </div>
      }
      @if (toBool(data?.isAdultsOnly)) {
        <div class="adult">18+</div>
      }
    </div>
  }
</ng-template>

<ng-template #ArticleDisplayedTagTemplate>
  @if (displayedTag()) {
    <a class="article-link article-tag" [routerLink]="tagLink()">{{ displayedTag()?.title }}</a>
  }
</ng-template>

<ng-template #ArticleSponsorshipTemplate>
  @if (hasSponsorship()) {
    <div class="sponsorship">Szponzorált tartalom</div>
  }
</ng-template>

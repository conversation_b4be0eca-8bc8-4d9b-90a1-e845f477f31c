@use 'shared' as *;

:host {
  --clip-path-height: 10px;
  --theme-color: var(--kui-black-950);

  display: block;
  width: 100%;
  margin-bottom: 20px;
  container-type: inline-size;
  kesma-icon {
    flex-shrink: 0;
  }
  .thumbnail {
    object-fit: cover;
    width: 100%;
    &.is-placeholder {
      object-fit: contain;
      background-color: var(--kui-red-500);
    }
  }
  .slant-thumbnail {
    clip-path: polygon(0 0, 100% 0, 101% 50%, 100% calc(100% - #{var(--clip-path-height)}), 0 100%, 0 100%);
  }
  .opinion-text {
    color: var(--kui-orange-500);
  }
  .exclusive-text {
    color: var(--exclusive-color);
  }
  .breaking-text {
    color: var(--breaking-color, var(--kui-red-500));
  }
  .article {
    background-color: var(--theme-background);
    display: flex;
    flex-direction: column;
    gap: 8px;
    &-link {
      color: var(--theme-color);
    }
    &-data {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    &-tag {
      font-size: 12px;
      font-weight: 500;
      line-height: 18px;
      text-transform: uppercase;
      padding-block: 3px;
      overflow-wrap: anywhere;
      color: var(--tag-color);
      transition: text-decoration 0.3s;
      text-decoration-color: transparent;
      &:hover {
        text-decoration: underline;
      }
    }
    &-bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 30px;
    }
    &-date {
      line-height: 24px;
      color: var(--theme-color);
      font-weight: 400;
    }
  }
  .title {
    font-size: 22px;
    line-height: 24px;
    transition: color 0.3s;
    &:hover {
      color: var(--hover-color);
    }
  }
  .sponsorship {
    position: absolute;
    top: 0;
    background-color: var(--kui-red-400);
    color: var(--kui-white);
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 600;
    line-height: normal;
    font-variant: all-small-caps;
    width: 100%;
    z-index: 10;
  }
  .badges {
    position: absolute;
    bottom: 10px;
    display: flex;
    gap: 8px;
    height: 24px;
    background-color: var(--kui-black-950);
    color: var(--kui-white);
    font-size: 12px;
    font-weight: 600;
    font-variant: all-small-caps;
    line-height: normal;
    align-items: center;
    z-index: 10;
    fill: var(--kui-white);
    kesma-icon {
      align-items: unset;
    }
    .with-icon {
      display: flex;
      gap: 4px;
      &:first-of-type {
        padding-left: 8px;
      }
      &:last-of-type {
        padding-right: 8px;
      }
    }
    .adult {
      padding: 4px 8px;
      background-color: var(--kui-red-400);
      height: 24px;
      border: 1px solid var(--kui-black-950);
      width: 34px;
    }
  }
  .comment-count {
    display: flex;
    font-size: 12px;
    font-weight: 600;
    align-items: center;
    color: var(--theme-color);
    margin-left: auto;
    gap: 6px;
    flex-shrink: 0;
    kesma-icon {
      fill: var(--theme-color);
    }
  }
  .relative {
    position: relative;
  }

  .left-border {
    border-left: 1px solid var(--theme-color);
    padding-left: 10px;
  }
  &.has-background {
    --theme-color: var(--kui-white);
    .article {
      gap: 0;
      &-data {
        margin: 16px 10px;
        // This is when the article card is displayed in a two-column layout on a mobile phone.
        // Such as the "most commented" box in the design.
        @container (max-width: 220px) {
          &:not(.no-image) {
            margin-top: 8px;
          }
        }

        &.left-border {
          margin-left: 0;
        }
      }
      &-link {
        color: var(--theme-color);
      }
    }
    .title {
      color: var(--theme-color);

      &:hover {
        color: var(--hover-color);
      }
    }
  }

  &.style-MainArticle {
    .comment-count {
      margin-left: unset;
    }
    .article {
      gap: 32px;
      @include container-breakpoint-down(sm) {
        gap: 16px;
      }
      &-data {
        text-align: center;
      }
      &-bottom {
        justify-content: center;
        gap: 32px;
      }
      @include media-breakpoint-up(md) {
        &-tag {
          font-weight: 400;
        }
      }
    }
    .badges {
      left: 32px;
      bottom: 32px;
      @include container-breakpoint-down(sm) {
        left: 10px;
        bottom: 10px;
      }
    }
    .title {
      font-size: 52px;
      font-weight: 800;
      line-height: 60px;
      @include container-breakpoint-down(sm) {
        font-size: 24px;
        line-height: 30px;
      }
    }
    &.has-background {
      .article {
        gap: 0;
        &-data {
          margin: 16px;
        }
      }
    }

    @include container-breakpoint-up(md) {
      .thumbnail {
        aspect-ratio: 16/8;
      }
      .slant-thumbnail {
        --clip-path-height: 30px;
      }
    }
  }

  &.style-TopSlantImgTagTitle {
    @include media-breakpoint-down(md) {
      .title {
        font-size: 18px;
        line-height: 22px;
      }
    }
  }

  &.style-TopSlantImgTagTitlePadding {
    .article {
      gap: 0;
      &-data {
        margin: 32px;
        @include container-breakpoint-down(xs) {
          margin: 16px;
        }

        &.left-border {
          margin-left: 0;
        }
      }
      .badges {
        left: 32px;
        bottom: 20px;
        @include container-breakpoint-down(xs) {
          left: 16px;
          bottom: 16px;
        }
      }
    }
    .title {
      font-size: 30px;
      font-weight: 800;
      line-height: 38px;
      @include container-breakpoint-down(xs) {
        font-size: 24px;
        line-height: 28px;
      }
    }
    @include container-breakpoint-up(sm) {
      .slant-thumbnail {
        --clip-path-height: 20px;
      }
    }
  }

  &.style-NoImgTagTitle {
    .sponsorship {
      position: initial;
    }
    .badges {
      width: fit-content;
      position: initial;
    }
    @include media-breakpoint-down(md) {
      .title {
        font-size: 18px;
        line-height: 22px;
      }
    }
    // This is when the article card is displayed in a two-column layout on a mobile phone.
    // Such as the "most commented" box in the design.
    @container (max-width: 220px) {
      .title {
        font-size: 16px;
        line-height: 18px;
      }
    }
  }

  &.style-TopImgTagTitle {
    @include media-breakpoint-up(lg) {
      &:not(.has-background) .article {
        gap: 16px;
      }
    }
    @include media-breakpoint-down(md) {
      .title {
        font-size: 16px;
        line-height: 18px;
      }
    }
  }

  &.style-SideImgDateTitleLead {
    .article {
      gap: 16px;
      @include container-breakpoint-up(sm) {
        gap: 32px;
        flex-direction: row;
        align-items: flex-start;
      }
      &-data,
      .thumbnail-wrapper {
        flex: 1;
      }
      &-bottom {
        justify-content: unset;
        width: fit-content;
      }
      @include container-breakpoint-up(sm) {
        &-data {
          gap: 16px;
        }
      }
    }
    .lead {
      font-size: 16px;
      line-height: 20px;
      color: var(--theme-color);
      @include container-breakpoint-up(sm) {
        margin-top: -8px;
        font-size: 18px;
        line-height: 26px;
      }
    }
    &.has-background {
      .article {
        gap: 0;
        align-items: unset;
        @include container-breakpoint-up(sm) {
          &-data {
            margin: 16px 10px;
          }
        }
      }
      .thumbnail {
        height: 100%;
      }
    }
    @include container-breakpoint-up(sm) {
      .title {
        font-size: 24px;
        line-height: 30px;
      }
      .badges {
        bottom: 20px;
      }
    }
  }

  &.style-HighlightedSideImgDateTitleLead {
    .article {
      gap: 16px;
      @include container-breakpoint-up(sm) {
        gap: 32px;
        flex-direction: row;
        align-items: flex-start;
      }
      &-data,
      .thumbnail-wrapper {
        flex: 1;
      }
      &-bottom {
        justify-content: unset;
        width: fit-content;
      }
      @include container-breakpoint-up(sm) {
        &-data {
          gap: 16px;
        }
      }
    }
    .title {
      font-size: 26px;
      font-style: normal;
      font-weight: 800;
      line-height: 28px; /* 116.667% */
    }
    .lead {
      font-size: 16px;
      line-height: 20px;
      color: var(--theme-color);
      @include container-breakpoint-up(sm) {
        margin-top: -8px;
        font-size: 22px;
        line-height: 28px;
      }
    }
    &.has-background {
      .article {
        gap: 0;
        align-items: unset;
        @include container-breakpoint-up(sm) {
          &-data {
            margin: 16px 10px;
          }
        }
      }
      .thumbnail {
        height: 100%;
      }
    }
    @include container-breakpoint-up(md) {
      .title {
        font-size: 36px;
        line-height: 42px; /* 116.667% */
      }
      .badges {
        bottom: 20px;
      }
    }
  }
  &.style-MostReadNews {
    .badges {
      width: fit-content;
      position: unset;
    }
    @include media-breakpoint-up(lg) {
      .title {
        font-size: 24px;
        line-height: 30px;
      }
      .article-data {
        gap: 16px;
      }
    }
  }

  &.style-MostRecentOrMostRead {
    .badges {
      width: fit-content;
      position: unset;
      margin-block: 6px;
    }
    .article {
      flex-direction: row;
      align-items: flex-start;
      gap: 16px;
      &-data {
        gap: 2px;
      }
      &-tag {
        color: var(--kui-red-500);
        &:hover {
          color: var(--kui-red-1100);
          text-decoration: underline;
        }
      }
    }
    .title {
      font-size: 18px;
      font-weight: 400;
      line-height: 26px;

      &:hover {
        color: var(--kui-red-500);
      }
    }
    @mixin mobile {
      .title {
        font-size: 16px;
        line-height: 20px;
      }
    }
    &.is-sidebar {
      @include mobile;
    }
    @include media-breakpoint-down(md) {
      @include mobile;
    }
  }

  &.style-MinuteToMinute {
    .title {
      font-size: 30px;
      line-height: 38px;
      font-weight: 800;
      @include media-breakpoint-down(md) {
        font-size: 24px;
        line-height: 28px;
      }
    }
  }

  &.style-ExternalRecommendation {
    .title {
      font-size: 16px;
      line-height: 18px;
      @include media-breakpoint-down(md) {
        font-size: 16px;
        line-height: 18px;
      }
    }
    .domain {
      font-size: 12px;
      text-transform: uppercase;
      line-height: 20px;
      color: var(--kui-red-500);
    }
  }

  &.style-ArticleRecommendationTopImg {
    .title {
      font-size: 16px;
      line-height: 18px;
    }
  }

  &.style-Weather {
    .article {
      gap: 16px;
      @include media-breakpoint-down(md) {
        gap: 8px;
      }
    }
    .title {
      font-size: 16px;
      line-height: 18px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 5;
      overflow: hidden;
      @include media-breakpoint-down(md) {
        font-size: 16px;
        line-height: 18px;
      }
    }
    .article-data {
      margin: 0;
      &:not(.no-image) {
        margin-top: 0;
      }
    }
  }

  &.style-ArticleRecommendationSideImg {
    border-left: 3px solid var(--kui-red-500);
    .article {
      gap: 32px;
      margin-left: 32px;
      @include container-breakpoint-down(xs) {
        margin-left: 16px;
      }
      @include container-breakpoint-up(sm) {
        flex-direction: row;
      }
      &-tag {
        font-weight: 400;
      }
    }
    .thumbnail {
      height: 100%;
      &-wrapper {
        flex-shrink: 0;
        width: 100%;
        @include container-breakpoint-up(sm) {
          max-width: 273px;
        }
      }
    }
    .left-border {
      border-left: unset;
      padding-left: initial;
    }
    .badges {
      width: fit-content;
      position: initial;
    }
    &.has-background {
      .article {
        gap: 0;
        &-data {
          margin: 16px 10px;
        }
      }
      .thumbnail {
        height: 100%;
      }
    }
    @include container-breakpoint-up(sm) {
      .title {
        font-size: 24px;
        line-height: 30px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 4;
        overflow: hidden;
      }
    }
  }

  &.style-ArticleRecommendationNoImg {
    margin-bottom: 0;
    border-bottom: 1px solid var(--kui-gray-200);
    padding-block: 12px;
    .title {
      font-size: 18px;
      line-height: 26px;
      @include media-breakpoint-down(md) {
        font-size: 16px;
        line-height: 24px;
      }
    }
    .badges {
      position: unset;
      width: fit-content;
      flex-shrink: 0;
    }
    .article {
      flex-direction: unset;
      container-type: inline-size;
      &-data {
        flex-direction: row-reverse;
        gap: 8px 16px;

        @include container-breakpoint-down(sm) {
          flex-direction: column-reverse;
        }
      }

      &-tag {
        white-space: nowrap;
      }
    }
  }
}

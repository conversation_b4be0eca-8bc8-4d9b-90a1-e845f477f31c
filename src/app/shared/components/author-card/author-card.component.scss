@use 'shared' as *;

:host {
  display: flex;
  gap: 16px 32px;
  @include media-breakpoint-down(xs) {
    flex-direction: column;
  }
  .author-card {
    &-avatar {
      width: 304px;
      height: 304px;
      aspect-ratio: 1/1;
      object-fit: cover;
      &.is-placeholder {
        object-fit: contain;
        background-color: var(--kui-red-500);
      }
      &-wrapper {
        flex-shrink: 0;
      }
      @include media-breakpoint-down(xs) {
        width: 100%;
        height: auto;
      }
    }
    &-data {
      display: flex;
      flex-direction: column;
      padding-bottom: 16px;
      gap: 16px;
    }
    &-title {
      font-size: 30px;
      line-height: 38px;
      @include media-breakpoint-down(md) {
        font-size: 24px;
        line-height: 28px;
      }
    }
    &-rank {
      margin-top: -8px;
      font-size: 18px;
      line-height: 22px;
      @include media-breakpoint-down(md) {
        font-size: 16px;
        line-height: 20px;
      }
    }
    &-social {
      display: flex;
      gap: 16px;
    }
    &-description {
      line-height: 24px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 6;
      overflow: hidden;
      @include media-breakpoint-down(md) {
        font-size: 14px;
        line-height: 20px;
      }
    }
    &-link {
      color: var(--kui-red-500);
      font-weight: 700;
      line-height: 20px;
      display: flex;
      width: fit-content;
      gap: 10px;
      &:hover {
        color: var(--kui-red-800);
        .arrow-right {
          fill: var(--kui-red-800);
        }
      }
    }
  }
  .hover-scale {
    transition: scale 300ms;
    &:hover {
      scale: 1.1;
    }
  }
  .arrow-right {
    fill: var(--kui-red-500);
  }
}

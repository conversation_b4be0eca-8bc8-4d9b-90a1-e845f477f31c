@use 'shared' as *;

$lifeColor: #ffadfb;
$mmeColor: #aeffd6;

:host {
  display: block;
  container-type: inline-size;
  color: var(--kui-white);
  width: 100%;
  @include media-breakpoint-down(md) {
    margin-inline: -15px;
    width: calc(100% + 30px);
  }
  .branding-box {
    padding: 32px;
    display: flex;
    flex-direction: column;
    @include container-breakpoint-down(md) {
      padding: 26px 17px;
    }
    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 8px;
      @include container-breakpoint-down(sm) {
        align-items: flex-start;
        flex-direction: column;
      }
    }
    &-link {
      color: inherit;
      fill: var(--kui-white);
      font-size: 16px;
      font-weight: 700;
      line-height: 20px;
      display: flex;
      gap: 10px;
    }
    &-data {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 32px;
      @include container-breakpoint-down(sm) {
        grid-template-columns: 1fr 1fr;
        gap: 16px;
      }
      @container (max-width: 360px) {
        grid-template-columns: 1fr;
      }
    }
  }
  .article {
    &-thumbnail {
      transition: scale 300ms;
      object-fit: cover;
      &-box {
        flex-shrink: 0;
        overflow: hidden;
        position: relative;
      }
    }
    &-title {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 5;
      overflow: hidden;
    }
    &:hover .article-thumbnail {
      scale: 1.1;
    }
  }
  &.life {
    background-color: #171517;
    .branding-box {
      gap: 35px;
      @include container-breakpoint-down(sm) {
        gap: 12px;
      }
      &-link:hover {
        color: $lifeColor;
        fill: $lifeColor;
      }
    }
    .article {
      font-family: var(--kui-font-poppins);
      &-link {
        color: var(--kui-white);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        &:hover {
          color: $lifeColor;
        }
      }
      &-category {
        font-size: 18px;
        line-height: 18px;
        text-transform: uppercase;
        font-weight: 500;
        letter-spacing: 0.72px;
        border-bottom: 4px solid $lifeColor;
        padding: 0 8px 4px 8px;
        @include container-breakpoint-down(sm) {
          font-size: 14px;
          line-height: 18px;
          letter-spacing: 0.56px;
        }
      }
      &-thumbnail {
        aspect-ratio: 1/1;
      }
      &-title {
        text-align: center;
        font-size: 20px;
        font-weight: 500;
        line-height: 30px;
        margin: 0 16px 16px;
        @include container-breakpoint-down(sm) {
          font-size: 14px;
          line-height: 16px;
        }
      }
    }
    .adult-icon {
      position: absolute;
      top: 20px;
      left: 20px;
      @include container-breakpoint-down(sm) {
        top: 10px;
        left: 12px;
        width: 24px;
      }
    }
  }
  &.mindmegette {
    background-color: #00964a;
    .branding-box {
      gap: 9px;
      @include container-breakpoint-down(sm) {
        gap: 12px;
      }
      &-logo {
        margin-top: 10px;
      }
      &-link:hover {
        color: $mmeColor;
        fill: $mmeColor;
      }
    }
    .article {
      color: #262626;
      border-radius: 8px;
      overflow: hidden;
      font-family: var(--kui-font-dm-sans);
      &-thumbnail {
        aspect-ratio: 4/3;
      }
      &-data {
        padding: 16px;
        background-color: var(--kui-white);
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      &-link {
        height: 100%;
        display: flex;
        flex-direction: column;
        &:hover {
          color: #00964a;
        }
      }
      &-category {
        background-color: #ff4d6a;
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 0.48px;
        text-transform: uppercase;
        border-radius: 4px;
        padding: 2px 6px;
        color: var(--kui-white);
      }
      &-badges {
        display: flex;
        margin-bottom: 4px;
        align-items: flex-start;
        gap: 6px;
      }
      &-title {
        font-size: 20px;
        line-height: 26px;
        margin-bottom: 8px;
        @include container-breakpoint-down(sm) {
          font-size: 14px;
          line-height: 16px;
        }
      }
      &-author {
        font-size: 14px;
        line-height: 20px;
        display: flex;
        align-items: center;
        margin-top: auto;
        gap: 8px;
      }
    }
    .video-icon {
      margin-left: auto;
      flex-shrink: 0;
    }
  }
}

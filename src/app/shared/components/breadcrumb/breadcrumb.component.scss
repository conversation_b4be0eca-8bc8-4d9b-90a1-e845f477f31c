@use 'shared' as *;

:host {
  display: block;
  color: var(--kui-black-950);
  font-size: 16px;
  width: 100%;

  .breadcrumb {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    &-link {
      line-height: 30px;
      color: var(--kui-red-500);

      &:hover {
        color: var(--kui-red-550);
      }
    }
  }

  .homepage {
    color: var(--kui-red-500);
    font-weight: 700;
    &:hover {
      color: var(--kui-red-550);
    }
  }

  .separator {
    margin-inline: 4px;
  }
}

@if (controlName(); as controlName) {
  <kesma-form-control>
    <label class="bors-form-label" [for]="controlName">
      {{ labelText() }}
      @if (isRequired()) {
        (kötelező)
      }
    </label>
    @if (useTextArea()) {
      <textarea class="bors-form-input" [formControlName]="controlName" [id]="controlName" type="text" [placeholder]="placeholder()" rows="5"></textarea>
    } @else {
      <input class="bors-form-input" [formControlName]="controlName" [id]="controlName" type="text" [placeholder]="placeholder()" />
    }
    <ng-content></ng-content>
  </kesma-form-control>
}

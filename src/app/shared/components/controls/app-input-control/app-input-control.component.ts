import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { KesmaFormControlComponent } from '@trendency/kesma-ui';
import { ControlContainer, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-input-control',
  templateUrl: './app-input-control.component.html',
  styleUrl: './app-input-control.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [KesmaFormControlComponent, ReactiveFormsModule],
  viewProviders: [
    {
      provide: ControlContainer,
      useExisting: FormGroupDirective,
    },
  ],
})
export class AppInputControlComponent {
  readonly controlName = input.required<string>();
  readonly labelText = input.required<string>();
  readonly placeholder = input<string>();
  readonly isRequired = input<boolean>(false);
  readonly useTextArea = input<boolean>(false);
}

@use 'shared' as *;

:host {
  .sponsor {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 4px 8px;
    border-style: solid;
    border-width: 1px;
    border-color: #194f71;

    .sponsor-details {
      display: flex;
      gap: 16px;
      align-items: center;
      flex-grow: 1;

      @include media-breakpoint-down(xs) {
        flex-direction: column;
      }

      &-image {
        width: 105px;
        max-width: 100%;
        vertical-align: middle;
        height: auto;
      }

      &-separator {
        width: 1px;
        height: 24px;
        background-color: #194f71;

        @include media-breakpoint-down(xs) {
          display: none;
        }
      }

      &-title {
        display: block;
        color: #194f71;
        transition: color 0.3s;
        font-weight: 500;
      }
    }

    .more-dossiers-container {
      padding: 8px 0px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #194f71;
      flex-grow: 0.2;
      transition: all 0.3s;
      color: #194f71;
      @include media-breakpoint-down(xs) {
        display: none;
      }

      .icon {
        fill: #194f71;
      }

      &:hover {
        opacity: 0.7;
      }
    }
  }

  .ad-label {
    color: var(--kui-bors-black);
    text-transform: uppercase;
    font-size: 12px;
    width: 100%;
    text-align: center;
    opacity: 0.75;
    margin-top: 5px;
    margin-bottom: 20px;

    @include media-breakpoint-down(sm) {
      margin-bottom: 30px;
    }
  }
}

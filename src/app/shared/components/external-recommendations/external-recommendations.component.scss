@use 'shared' as *;

.external-recommendations {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32px;

  @include container-breakpoint-down(sm) {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .full-row {
    grid-column: 1 / -1;
  }

  app-article-card {
    margin-bottom: 0;
  }
}

.block {
  margin-bottom: 32px;

  @include media-breakpoint-down(md) {
    margin-bottom: 16px;
  }
}

import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { IconComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'app-quiz-mini-card',
  templateUrl: './quiz-mini-card.component.html',
  styleUrls: ['./quiz-mini-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, IconComponent],
})
export class QuizMiniCardComponent {
  readonly title = input.required<string>();
  readonly link = input.required<string | string[]>();
}

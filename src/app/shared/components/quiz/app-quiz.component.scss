@use 'shared' as *;

$errorColor: #d40d00;
$successColor: #007b24;

:host {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 32px;
  border-left: 3px solid var(--kui-red-500);
  color: var(--kui-black-950);
  gap: 32px;
  @include media-breakpoint-down(md) {
    padding: 16px;
    gap: 16px;
  }
  .quiz {
    &-question {
      display: flex;
      flex-direction: column;
      gap: 8px;
      &-text {
        font-size: 24px;
        font-weight: 700;
        line-height: 30px;
        @include media-breakpoint-down(md) {
          font-size: 20px;
          line-height: 24px;
        }
      }
      &-image {
        object-fit: cover;
        aspect-ratio: 3/2;
        width: 100%;
      }
    }
    &-stepper {
      font-size: 16px;
      line-height: 24px;
      @include media-breakpoint-down(md) {
        line-height: 30px;
      }
    }
    &-result-share {
      display: flex;
      background-color: var(--kui-red-500);
      cursor: pointer;
      height: 40px;
      padding-inline: 16px;
      width: fit-content;
      align-items: center;
      .share-link {
        font-size: 16px;
        line-height: 20px;
        color: var(--kui-white);
        font-weight: bold;
      }
      &:hover {
        background-color: var(--kui-yellow-500);
        .share-link {
          color: var(--kui-black-950);
        }
      }
    }
  }
  .answer {
    &-list {
      border-top: 1px solid var(--kui-gray-250);
    }
    &-list-item {
      padding: 16px;
      border-bottom: 1px solid var(--kui-gray-250);
      display: flex;
      align-items: center;
      gap: 16px;
      font-size: 18px;
      line-height: 20px;
      cursor: pointer;
      @include media-breakpoint-down(md) {
        font-size: 16px;
        line-height: 24px;
      }
      &.wrong,
      &.correct {
        color: var(--kui-white);
        .radio-label {
          font-weight: 700;
        }
        .result-label {
          display: block;
        }
      }
      &.wrong {
        background-color: $errorColor;
      }
      &.correct {
        background-color: $successColor;
        fill: var(--kui-white);
      }
      &.correct-answer {
        color: $successColor;
        fill: $successColor;
        .result-label {
          display: block;
        }
      }
      &.disabled {
        cursor: initial;
        .circle {
          color: var(--kui-black-200);
        }
      }
    }
  }
  .result-label {
    margin-left: auto;
    display: none;
  }
  .radio-label {
    margin-right: 16px;
    @include media-breakpoint-down(md) {
      margin-right: 0;
    }
  }
  .wrong {
    fill: var(--kui-white);
  }
  kesma-icon {
    flex-shrink: 0;
  }
  .more-quiz-button {
    margin-top: 16px;
  }
}

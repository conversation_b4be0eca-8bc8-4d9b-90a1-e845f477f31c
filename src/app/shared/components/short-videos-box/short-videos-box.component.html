<h2 class="title">SHORTS</h2>
<div
  kesma-swipe
  class="swipe"
  [data]="shortVideos()"
  [itemTemplate]="itemTemplate"
  [breakpoints]="breakpoints"
  [previousNavigationTemplate]="previousNavigation"
  [nextNavigationTemplate]="nextNavigation"
  [useNavigation]="true"
></div>

<a class="more-short-videos" [routerLink]="['/', 'shorts']" attr.aria-label="Gyorsvideók">
  <span>Még Gyorsvideók <kesma-icon name="arrow-right-long" [size]="16"></kesma-icon></span>
</a>

<ng-template #itemTemplate let-data="data">
  <div class="short-video">
    <div class="short-video-wrapper">
      <a [routerLink]="['/', 'shorts', data?.slug]" [attr.aria-label]="data?.title">
        <img [src]="data?.thumbnail?.url || 'assets/images/placeholder.svg'" [alt]="data?.title" />
      </a>
      <a [routerLink]="['/', 'shorts', data?.slug]" [attr.aria-label]="data?.title" class="short-video-duration-wrapper">
        <span class="short-video-badge"> <kesma-icon name="video_white" [size]="16"></kesma-icon>VIDEÓ</span>
        @if (data?.length; as duration) {
          <span class="short-video-duration">{{ duration | intervalToDuration }}</span>
        }
      </a>
    </div>
    @if (data?.title; as title) {
      <a [routerLink]="['/', 'shorts', data?.slug]" class="short-video-title" [attr.aria-label]="title">
        <span> {{ title }}</span>
      </a>
    }
  </div>
</ng-template>

<ng-template #previousNavigation>
  <span class="navigation-button">
    <kesma-icon name="chevron-left" [size]="40"></kesma-icon>
  </span>
</ng-template>

<ng-template #nextNavigation>
  <span class="navigation-button">
    <kesma-icon name="chevron-right" [size]="40"></kesma-icon>
  </span>
</ng-template>

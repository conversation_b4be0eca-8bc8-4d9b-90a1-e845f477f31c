@use 'shared' as *;

:host {
  padding: 32px;
  padding-right: 0;
  display: flex;
  flex-direction: column;
  gap: 32px;
  background-color: var(--kui-black-950);

  @include media-breakpoint-down(sm) {
    gap: 16px;
    padding: 16px;
  }

  .title {
    font-size: 48px;
    font-weight: 800;
    line-height: 42px;
    color: var(--kui-white);

    @include media-breakpoint-down(sm) {
      font-size: 28px;
      line-height: 28px;
    }
  }

  .short-video {
    width: fit-content;

    &-wrapper {
      height: 484px;
      position: relative;

      @include media-breakpoint-down(sm) {
        height: 292px;
      }

      img {
        height: 100%;
        object-fit: cover;
      }
    }

    &-badge {
      padding: 4px 8px;
      background-color: var(--kui-black-950);
      font-size: 12px;
      font-weight: 600;
      line-height: normal;
      color: var(--kui-white);

      display: flex;
      align-items: center;
      gap: 4px;
    }

    &-duration {
      background-color: var(--kui-white);
      padding: 4px 8px;
      font-size: 12px;
      font-weight: 600;
      line-height: normal;
      color: var(--kui-black-950);

      &-wrapper {
        position: absolute;
        display: flex;
        bottom: 10px;
        left: 0;
        height: 24px;
        z-index: 1;
      }
    }

    &-title {
      padding-top: 8px;
      color: var(--kui-white);
      display: block;

      span {
        font-size: 20px;
        font-weight: 700;
        line-height: 24px;

        @include media-breakpoint-down(sm) {
          font-size: 14px;
          line-height: 16px;
        }
      }
    }
  }

  .more-short-videos {
    color: var(--kui-white);
    span {
      font-size: 16px;
      font-weight: 700;
      line-height: 20px;

      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .navigation-button {
    height: 40px;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--kui-white);
  }

  .swipe::ng-deep {
    .item-container {
      padding-right: 32px;

      @include media-breakpoint-down(sm) {
        padding-right: 0;
      }
    }
    .bottom-navigation {
      display: inline-flex;
      position: absolute;
      top: -75px;
      right: 32px;
      gap: 16px;

      @include media-breakpoint-down(sm) {
        gap: 8px;
        top: -50px;
        right: 0;
      }
    }
  }
}

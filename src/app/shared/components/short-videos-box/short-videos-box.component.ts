import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { VideoCard, KesmaSwipeComponent, SwipeBreakpoints, IconComponent, IntervalToDurationPipe } from '@trendency/kesma-ui';

@Component({
  selector: 'app-short-videos-box',
  imports: [KesmaSwipeComponent, IconComponent, IntervalToDurationPipe, RouterLink],
  templateUrl: './short-videos-box.component.html',
  styleUrl: './short-videos-box.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ShortVideosBoxComponent {
  readonly shortVideos = input.required<VideoCard[]>();

  readonly breakpoints: SwipeBreakpoints = {
    default: {
      itemCount: 2,
      gap: '16px',
    },
    768: {
      itemCount: 3.5,
      gap: '32px',
    },

    992: {
      itemCount: 4.5,
      gap: '32px',
    },
  };
}

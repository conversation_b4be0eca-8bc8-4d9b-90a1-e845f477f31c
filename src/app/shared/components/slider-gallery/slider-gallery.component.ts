import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { NgTemplateOutlet } from '@angular/common';
import {
  AdultOverlayComponent,
  ClickStopPropagationDirective,
  GalleryData,
  IconComponent,
  KesmaSwipeComponent,
  KesmaSwipeSliderGalleryComponent,
} from '@trendency/kesma-ui';

export type SliderGalleryFullscreenLayerClickedEvent = {
  gallery: GalleryData;
  selectedImageIndex?: number;
};

@Component({
  selector: 'app-bors-slider-gallery',
  imports: [NgTemplateOutlet, AdultOverlayComponent, ClickStopPropagationDirective, IconComponent, KesmaSwipeComponent],
  templateUrl: './slider-gallery.component.html',
  styleUrl: './slider-gallery.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  host: { ngSkipHydration: 'true' },
})
export class BorsSliderGalleryComponent extends KesmaSwipeSliderGalleryComponent {
  highlightedImageId = input<string>();

  highlightedImageIndex = computed(() => {
    return (this.data?.images || []).findIndex(({ id }) => id === this.highlightedImageId()) || 0;
  });

  fullscreenLayerClicked = output<SliderGalleryFullscreenLayerClickedEvent>();

  currentIndex = computed(() => this.swipeComponent()?.currentIndex() || 0);

  override onOpenSliderLayer(): void {
    // We cannot open the layer gallery here, as it needs to be on a dedicated route.
    if (this.data) {
      this.fullscreenLayerClicked.emit({ gallery: this.data, selectedImageIndex: this.currentIndex() });
    }
  }

  swipeNext(): void {
    this.swipeComponent()?.swipePageForward();
  }

  swipePrev(): void {
    this.swipeComponent()?.swipePageBack();
  }
}

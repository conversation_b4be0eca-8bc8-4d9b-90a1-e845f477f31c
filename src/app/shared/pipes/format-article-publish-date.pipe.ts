import { Pipe, PipeTransform } from '@angular/core';
import { format, formatDate, isToday, isYesterday } from 'date-fns';
import { backendDateToDate } from '@trendency/kesma-ui';
import { hu } from 'date-fns/locale';

@Pipe({
  name: 'archiveArticlePublishDatePipe',
})
export class FormatArticlePublishDatePipe implements PipeTransform {
  transform(date: string): string {
    if (!date) {
      return '';
    }
    const zonedDate = backendDateToDate(date) as Date;
    if (isToday(zonedDate)) {
      return `ma, ${format(zonedDate, 'HH:mm')}`;
    }
    if (isYesterday(zonedDate)) {
      return `tegnap, ${format(zonedDate, 'HH:mm')}`;
    }
    return formatDate(zonedDate, 'eeee, HH:mm', { locale: hu });
  }
}

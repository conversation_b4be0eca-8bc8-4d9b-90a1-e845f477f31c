import { EnvironmentApiUrl, ReqService, UtilService } from '@trendency/kesma-core';
import { environment } from '../../../environments/environment';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  Api<PERSON>ist<PERSON><PERSON>ult,
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  BackendComment,
  BackendCommentWithArticle,
  ReactionsData,
  ThumbnailImage,
  User,
} from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';
import { PortalUser } from '../definitions';
import { backendPortalUserDataToUser } from '../utils';
import { CommentableType, CommentRequestParams, Reaction } from '../../feature/comments/api/comment.definitions';
import { getCommentOrderParam } from '../../feature/comments/utils/comment.utils';
import { ProfileEditFormData } from '../definitions/profil.definitions';
import { profileEditFormDataToBackendRequest } from '../utils/profile.utils';

@Injectable({
  providedIn: 'root',
})
export class SecureApiService {
  private readonly reqService = inject(ReqService);
  private readonly utilService = inject(UtilService);

  get secureApiUrl(): string {
    if (typeof environment.secureApiUrl === 'string') {
      return environment.secureApiUrl;
    }
    const { clientApiUrl, serverApiUrl } = environment.secureApiUrl as EnvironmentApiUrl;
    return this.utilService.isBrowser() ? clientApiUrl : serverApiUrl;
  }

  getCurrentUser(): Observable<User> {
    return this.reqService.get<PortalUser>(`${this.secureApiUrl}/portal-user`).pipe(map((user) => backendPortalUserDataToUser(user)));
  }

  logout(): Observable<void> {
    return this.reqService.get(`${this.secureApiUrl}/logout`);
  }

  readonly comments = {
    list: (type: CommentableType, id: string, params: CommentRequestParams): Observable<ApiListResult<BackendComment>> =>
      this.reqService.get(`${this.secureApiUrl}/comments/${type}/${id}/${type === 'article' ? 'comments' : 'answers'}`, {
        params: {
          page_limit: params.page_limit?.toString(),
          rowCount_limit: params.rowCount_limit?.toString(),
          ...getCommentOrderParam(params.order),
        },
      }),

    add: (type: CommentableType, id: string, text: string): Observable<void> =>
      this.reqService.post(`${this.secureApiUrl}/comments/${type}/${id}/create`, { text }),

    update: (type: CommentableType, id: string, text: string): Observable<void> =>
      this.reqService.post(`${this.secureApiUrl}/comments/${type}/${id}/update`, { text }),

    vote: (type: CommentableType, id: string, reaction: Reaction): Observable<void> =>
      this.reqService.post(`${this.secureApiUrl}/comments/${type}/${id}/${reaction}`, {}),

    clearReaction: (type: CommentableType, id: string): Observable<void> =>
      this.reqService.post(`${this.secureApiUrl}/comments/${type}/${id}/clear-like-dislike`, {}),

    report: (type: CommentableType, id: string): Observable<void> => this.reqService.post(`${this.secureApiUrl}/comments/${type}/${id}/report`, {}),

    getUserVotes: (type: CommentableType, id: string, userId: string): Observable<ApiResult<ReactionsData>> => {
      if (type === 'article') {
        return this.reqService.get(`${this.secureApiUrl}/comments/${type}/${id}/my-votes`);
      }
      return this.reqService.get(`${this.secureApiUrl}/comments/portal-user/${userId}/${type}/${id}/my-votes`);
    },
    // Note: We can't access AuthService here because it would create a circular dependency
  };

  editCurrentUser(formData: ProfileEditFormData): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/portal-user/save`, profileEditFormDataToBackendRequest(formData));
  }

  deleteAccount(passwordOld: string): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/portal-user/delete-account`, { password: passwordOld });
  }

  getMyComments(params: object, userId: string): Observable<ApiResult<BackendCommentWithArticle[], ApiResponseMetaList>> {
    return this.reqService.get<ApiResult<BackendCommentWithArticle[], ApiResponseMetaList>>(`${this.secureApiUrl}/comments/portal-user/${userId}/my-comments`, {
      params,
    });
  }

  getSavedArticlesList(page = 0, itemsPerPage = 5): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    const params: Record<string, string> = {
      rowCount_limit: itemsPerPage?.toString(),
      page_limit: page?.toString(),
    };
    return this.reqService
      .get<ApiResult<ArticleCard[], ApiResponseMetaList>>(`${this.secureApiUrl}/saved-articles/list`, {
        params,
      })
      .pipe(
        map((apiResult) => ({
          meta: apiResult.meta,
          data: apiResult.data?.map((article: ArticleCard) => ({
            ...article,
            thumbnail: {
              url: (article.thumbnail as unknown as string) ?? '',
              // BE returns thumbnail as string instead of object here, unlike in other places
            } as ThumbnailImage,
            hasGallery: article.hasGallery ? !!+article.hasGallery : false,
          })),
        }))
      );
  }

  getSavedArticleCount(): Observable<ApiResult<{ savedArticleCount: number }>> {
    return this.reqService.get(`${this.secureApiUrl}/saved-articles/count`, {});
  }

  getSavedArticle(articleId: string): Observable<ApiResult<{ saved: boolean }>> {
    return this.reqService.get(`${this.secureApiUrl}/saved-articles/${articleId}`, {});
  }

  postSavedArticle(articleId: string): Observable<ApiResult<never>> {
    return this.reqService.post(`${this.secureApiUrl}/saved-articles/save/${articleId}`, {});
  }

  deleteSavedArticles(articleId: string): Observable<ApiResult<never>> {
    return this.reqService.delete(`${this.secureApiUrl}/saved-articles/delete/${articleId}`, {});
  }

  submitDoctorAnswerQuestion(subject: string, question: string): Observable<ApiResult<never>> {
    return this.reqService.post(`${this.secureApiUrl}/doctor-answer/form/submit `, {
      subject,
      question,
    });
  }
}

import { BreadcrumbItem } from '@trendency/kesma-ui';
import { BreadcrumbList, BreadcrumbListElement } from '@trendency/kesma-core';
import { environment } from 'src/environments/environment';

export const DEFAULT_BREADCRUMB_ITEM: BreadcrumbItem = { label: 'BORS', url: ['/'] };

export const makeBreadcrumbSchema = (items: BreadcrumbItem[]): BreadcrumbList => {
  return {
    '@type': 'BreadcrumbList',
    itemListElement: <BreadcrumbListElement[]>items?.map((item, index) => {
      let url = '';

      if (item?.url) {
        if (typeof item?.url === 'string') {
          url = environment.siteUrl + item.url;
        } else if (Array.isArray(item?.url)) {
          url = environment.siteUrl + (item?.url as [])?.join('/')?.substring(1);
        }
      }

      return {
        '@type': 'ListItem',
        position: index + 1,
        item: {
          '@id': url,
          name: item.label,
        },
      };
    }),
  };
};

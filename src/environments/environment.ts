import { Environment } from '@trendency/kesma-core';

// <PERSON><PERSON><PERSON> fejlesztői környezet
export const environment: Environment = {
  production: false,
  type: 'local',
  apiUrl: 'https://kozponti-api.dev.trendency.hu/publicapi/hu', // for proxy: '/publicapi/hu' then: npm run start-with-proxy
  secureApiUrl: 'https://kozponti-api.dev.trendency.hu/secureapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  facebookAppId: '',
  siteUrl: 'http://localhost:4200',
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1',
  googleTagManager: 'GTM-TF2DM9H',
  gemiusId: 'nXblnbOxpYU_uhguHmC_VZZW3yiFlPtF0fxOn20TBQT.p7',
  httpReqTimeout: 30, // second
  sentry: {
    dsn: '', // Disable sentry for localhost (empty dsn)
    tracingOrigins: ['http://localhost:4200', 'localhost'],
    sampleRate: 0.1,
    tracesSampleRate: 0.1,
    profilesSampleRate: 0.1,
  },
};

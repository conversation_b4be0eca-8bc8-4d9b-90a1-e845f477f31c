@use 'abstracts/var-exports' as *;
@use 'abstracts/mixins' as *;

// kesma-ui
@use 'node_modules/@trendency/kesma-ui/src/scss/kesma-ui-export.scss' as *;
@use 'node_modules/@trendency/kesma-ui/src/scss/abstracts/_kesma-ui-variables.scss' as *;

// Base scss
@use 'base/reset' as *;
@use 'base/typography' as *;
@use 'base/fonts' as *;

// Layout
@use 'layout' as *;

// Components
@use 'components' as *;

// Focus points
@use '../../node_modules/@trendency/kesma-ui/src/scss/base/utilities.scss' as *;

//ng-select
@use '@ng-select/ng-select/themes/default.theme.css' as *;

:root {
  @include kesma-ui-variables;
  @include export-custom-vars;

  --ad-margin: 32px 0px;
}

@include layoutMakeExplicitColumnsSticky();
